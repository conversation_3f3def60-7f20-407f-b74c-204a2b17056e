apiVersion: apps/v1
kind: Deployment
metadata:
  name: sample
  labels:
    app: busybox
spec:
  replicas: 5
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: busybox
  template:
    metadata:
      labels:
        app: busybox
    spec:
      containers:
        - name: busybox
          image: busybox:1.32
          imagePullPolicy: IfNotPresent
          command: ["/bin/sh", "-c", "sleep 10000"]
          resources:
            limits:
              memory: "10Mi"
              cpu: "10m"
