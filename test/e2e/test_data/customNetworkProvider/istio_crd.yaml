apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: virtualservices.networking.istio.io
spec:
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: VirtualService
    listKind: VirtualServiceList
    plural: virtualservices
    shortNames:
    - vs
    singular: virtualservice
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The names of gateways and sidecars that should apply these routes
      jsonPath: .spec.gateways
      name: Gateways
      type: string
    - description: The destination hosts to which traffic is being sent
      jsonPath: .spec.hosts
      name: Hosts
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha3
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting label/content routing, sni routing,
              etc. See more details at: https://istio.io/docs/reference/config/networking/virtual-service.html'
            properties:
              exportTo:
                description: A list of namespaces to which this virtual service is
                  exported.
                items:
                  type: string
                type: array
              gateways:
                description: The names of gateways and sidecars that should apply
                  these routes.
                items:
                  type: string
                type: array
              hosts:
                description: The destination hosts to which traffic is being sent.
                items:
                  type: string
                type: array
              http:
                description: An ordered list of route rules for HTTP traffic.
                items:
                  properties:
                    corsPolicy:
                      description: Cross-Origin Resource Sharing policy (CORS).
                      properties:
                        allowCredentials:
                          nullable: true
                          type: boolean
                        allowHeaders:
                          items:
                            type: string
                          type: array
                        allowMethods:
                          description: List of HTTP methods allowed to access the
                            resource.
                          items:
                            type: string
                          type: array
                        allowOrigin:
                          description: The list of origins that are allowed to perform
                            CORS requests.
                          items:
                            type: string
                          type: array
                        allowOrigins:
                          description: String patterns that match allowed origins.
                          items:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          type: array
                        exposeHeaders:
                          items:
                            type: string
                          type: array
                        maxAge:
                          type: string
                      type: object
                    delegate:
                      properties:
                        name:
                          description: Name specifies the name of the delegate VirtualService.
                          type: string
                        namespace:
                          description: Namespace specifies the namespace where the
                            delegate VirtualService resides.
                          type: string
                      type: object
                    directResponse:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      properties:
                        body:
                          description: Specifies the content of the response body.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - string
                              - required:
                                - bytes
                          - required:
                            - string
                          - required:
                            - bytes
                          properties:
                            bytes:
                              description: response body as base64 encoded bytes.
                              format: binary
                              type: string
                            string:
                              type: string
                          type: object
                        status:
                          description: Specifies the HTTP response status to be returned.
                          type: integer
                      type: object
                    fault:
                      description: Fault injection policy to apply on HTTP traffic
                        at the client side.
                      properties:
                        abort:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - httpStatus
                              - required:
                                - grpcStatus
                              - required:
                                - http2Error
                          - required:
                            - httpStatus
                          - required:
                            - grpcStatus
                          - required:
                            - http2Error
                          properties:
                            grpcStatus:
                              description: GRPC status code to use to abort the request.
                              type: string
                            http2Error:
                              type: string
                            httpStatus:
                              description: HTTP status code to use to abort the Http
                                request.
                              format: int32
                              type: integer
                            percentage:
                              description: Percentage of requests to be aborted with
                                the error code provided.
                              properties:
                                value:
                                  format: double
                                  type: number
                              type: object
                          type: object
                        delay:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - fixedDelay
                              - required:
                                - exponentialDelay
                          - required:
                            - fixedDelay
                          - required:
                            - exponentialDelay
                          properties:
                            exponentialDelay:
                              type: string
                            fixedDelay:
                              description: Add a fixed delay before forwarding the
                                request.
                              type: string
                            percent:
                              description: Percentage of requests on which the delay
                                will be injected (0-100).
                              format: int32
                              type: integer
                            percentage:
                              description: Percentage of requests on which the delay
                                will be injected.
                              properties:
                                value:
                                  format: double
                                  type: number
                              type: object
                          type: object
                      type: object
                    headers:
                      properties:
                        request:
                          properties:
                            add:
                              additionalProperties:
                                type: string
                              type: object
                            remove:
                              items:
                                type: string
                              type: array
                            set:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                        response:
                          properties:
                            add:
                              additionalProperties:
                                type: string
                              type: object
                            remove:
                              items:
                                type: string
                              type: array
                            set:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                      type: object
                    match:
                      items:
                        properties:
                          authority:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          headers:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            type: object
                          ignoreUriCase:
                            description: Flag to specify whether the URI matching
                              should be case-insensitive.
                            type: boolean
                          method:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          name:
                            description: The name assigned to a match.
                            type: string
                          port:
                            description: Specifies the ports on the host that is being
                              addressed.
                            type: integer
                          queryParams:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            description: Query parameters for matching.
                            type: object
                          scheme:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                          statPrefix:
                            description: The human readable prefix to use when emitting
                              statistics for this route.
                            type: string
                          uri:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          withoutHeaders:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            description: withoutHeader has the same syntax with the
                              header, but has opposite meaning.
                            type: object
                        type: object
                      type: array
                    mirror:
                      properties:
                        host:
                          description: The name of a service from the service registry.
                          type: string
                        port:
                          description: Specifies the port on the host that is being
                            addressed.
                          properties:
                            number:
                              type: integer
                          type: object
                        subset:
                          description: The name of a subset within the service.
                          type: string
                      type: object
                    mirror_percent:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      nullable: true
                      type: integer
                    mirrorPercent:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      nullable: true
                      type: integer
                    mirrorPercentage:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      properties:
                        value:
                          format: double
                          type: number
                      type: object
                    mirrors:
                      items:
                        properties:
                          destination:
                            description: Destination specifies the target of the mirror
                              operation.
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          percentage:
                            description: Percentage of the traffic to be mirrored
                              by the `destination` field.
                            properties:
                              value:
                                format: double
                                type: number
                            type: object
                        type: object
                      type: array
                    name:
                      description: The name assigned to the route for debugging purposes.
                      type: string
                    redirect:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      oneOf:
                      - not:
                          anyOf:
                          - required:
                            - port
                          - required:
                            - derivePort
                      - required:
                        - port
                      - required:
                        - derivePort
                      properties:
                        authority:
                          type: string
                        derivePort:
                          enum:
                          - FROM_PROTOCOL_DEFAULT
                          - FROM_REQUEST_PORT
                          type: string
                        port:
                          description: On a redirect, overwrite the port portion of
                            the URL with this value.
                          type: integer
                        redirectCode:
                          type: integer
                        scheme:
                          description: On a redirect, overwrite the scheme portion
                            of the URL with this value.
                          type: string
                        uri:
                          type: string
                      type: object
                    retries:
                      description: Retry policy for HTTP requests.
                      properties:
                        attempts:
                          description: Number of retries to be allowed for a given
                            request.
                          format: int32
                          type: integer
                        perTryTimeout:
                          description: Timeout per attempt for a given request, including
                            the initial call and any retries.
                          type: string
                        retryOn:
                          description: Specifies the conditions under which retry
                            takes place.
                          type: string
                        retryRemoteLocalities:
                          description: Flag to specify whether the retries should
                            retry to other localities.
                          nullable: true
                          type: boolean
                      type: object
                    rewrite:
                      description: Rewrite HTTP URIs and Authority headers.
                      properties:
                        authority:
                          description: rewrite the Authority/Host header with this
                            value.
                          type: string
                        uri:
                          type: string
                        uriRegexRewrite:
                          description: rewrite the path portion of the URI with the
                            specified regex.
                          properties:
                            match:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              type: string
                            rewrite:
                              description: The string that should replace into matching
                                portions of original URI.
                              type: string
                          type: object
                      type: object
                    route:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          headers:
                            properties:
                              request:
                                properties:
                                  add:
                                    additionalProperties:
                                      type: string
                                    type: object
                                  remove:
                                    items:
                                      type: string
                                    type: array
                                  set:
                                    additionalProperties:
                                      type: string
                                    type: object
                                type: object
                              response:
                                properties:
                                  add:
                                    additionalProperties:
                                      type: string
                                    type: object
                                  remove:
                                    items:
                                      type: string
                                    type: array
                                  set:
                                    additionalProperties:
                                      type: string
                                    type: object
                                type: object
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                    timeout:
                      description: Timeout for HTTP requests, default is disabled.
                      type: string
                  type: object
                type: array
              tcp:
                description: An ordered list of route rules for opaque TCP traffic.
                items:
                  properties:
                    match:
                      items:
                        properties:
                          destinationSubnets:
                            description: IPv4 or IPv6 ip addresses of destination
                              with optional subnet.
                            items:
                              type: string
                            type: array
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          port:
                            description: Specifies the port on the host that is being
                              addressed.
                            type: integer
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                          sourceSubnet:
                            description: IPv4 or IPv6 ip address of source with optional
                              subnet.
                            type: string
                        type: object
                      type: array
                    route:
                      description: The destination to which the connection should
                        be forwarded to.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                  type: object
                type: array
              tls:
                items:
                  properties:
                    match:
                      items:
                        properties:
                          destinationSubnets:
                            description: IPv4 or IPv6 ip addresses of destination
                              with optional subnet.
                            items:
                              type: string
                            type: array
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          port:
                            description: Specifies the port on the host that is being
                              addressed.
                            type: integer
                          sniHosts:
                            description: SNI (server name indicator) to match on.
                            items:
                              type: string
                            type: array
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                        type: object
                      type: array
                    route:
                      description: The destination to which the connection should
                        be forwarded to.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                  type: object
                type: array
            type: object
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The names of gateways and sidecars that should apply these routes
      jsonPath: .spec.gateways
      name: Gateways
      type: string
    - description: The destination hosts to which traffic is being sent
      jsonPath: .spec.hosts
      name: Hosts
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting label/content routing, sni routing,
              etc. See more details at: https://istio.io/docs/reference/config/networking/virtual-service.html'
            properties:
              exportTo:
                description: A list of namespaces to which this virtual service is
                  exported.
                items:
                  type: string
                type: array
              gateways:
                description: The names of gateways and sidecars that should apply
                  these routes.
                items:
                  type: string
                type: array
              hosts:
                description: The destination hosts to which traffic is being sent.
                items:
                  type: string
                type: array
              http:
                description: An ordered list of route rules for HTTP traffic.
                items:
                  properties:
                    corsPolicy:
                      description: Cross-Origin Resource Sharing policy (CORS).
                      properties:
                        allowCredentials:
                          nullable: true
                          type: boolean
                        allowHeaders:
                          items:
                            type: string
                          type: array
                        allowMethods:
                          description: List of HTTP methods allowed to access the
                            resource.
                          items:
                            type: string
                          type: array
                        allowOrigin:
                          description: The list of origins that are allowed to perform
                            CORS requests.
                          items:
                            type: string
                          type: array
                        allowOrigins:
                          description: String patterns that match allowed origins.
                          items:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          type: array
                        exposeHeaders:
                          items:
                            type: string
                          type: array
                        maxAge:
                          type: string
                      type: object
                    delegate:
                      properties:
                        name:
                          description: Name specifies the name of the delegate VirtualService.
                          type: string
                        namespace:
                          description: Namespace specifies the namespace where the
                            delegate VirtualService resides.
                          type: string
                      type: object
                    directResponse:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      properties:
                        body:
                          description: Specifies the content of the response body.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - string
                              - required:
                                - bytes
                          - required:
                            - string
                          - required:
                            - bytes
                          properties:
                            bytes:
                              description: response body as base64 encoded bytes.
                              format: binary
                              type: string
                            string:
                              type: string
                          type: object
                        status:
                          description: Specifies the HTTP response status to be returned.
                          type: integer
                      type: object
                    fault:
                      description: Fault injection policy to apply on HTTP traffic
                        at the client side.
                      properties:
                        abort:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - httpStatus
                              - required:
                                - grpcStatus
                              - required:
                                - http2Error
                          - required:
                            - httpStatus
                          - required:
                            - grpcStatus
                          - required:
                            - http2Error
                          properties:
                            grpcStatus:
                              description: GRPC status code to use to abort the request.
                              type: string
                            http2Error:
                              type: string
                            httpStatus:
                              description: HTTP status code to use to abort the Http
                                request.
                              format: int32
                              type: integer
                            percentage:
                              description: Percentage of requests to be aborted with
                                the error code provided.
                              properties:
                                value:
                                  format: double
                                  type: number
                              type: object
                          type: object
                        delay:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - fixedDelay
                              - required:
                                - exponentialDelay
                          - required:
                            - fixedDelay
                          - required:
                            - exponentialDelay
                          properties:
                            exponentialDelay:
                              type: string
                            fixedDelay:
                              description: Add a fixed delay before forwarding the
                                request.
                              type: string
                            percent:
                              description: Percentage of requests on which the delay
                                will be injected (0-100).
                              format: int32
                              type: integer
                            percentage:
                              description: Percentage of requests on which the delay
                                will be injected.
                              properties:
                                value:
                                  format: double
                                  type: number
                              type: object
                          type: object
                      type: object
                    headers:
                      properties:
                        request:
                          properties:
                            add:
                              additionalProperties:
                                type: string
                              type: object
                            remove:
                              items:
                                type: string
                              type: array
                            set:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                        response:
                          properties:
                            add:
                              additionalProperties:
                                type: string
                              type: object
                            remove:
                              items:
                                type: string
                              type: array
                            set:
                              additionalProperties:
                                type: string
                              type: object
                          type: object
                      type: object
                    match:
                      items:
                        properties:
                          authority:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          headers:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            type: object
                          ignoreUriCase:
                            description: Flag to specify whether the URI matching
                              should be case-insensitive.
                            type: boolean
                          method:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          name:
                            description: The name assigned to a match.
                            type: string
                          port:
                            description: Specifies the ports on the host that is being
                              addressed.
                            type: integer
                          queryParams:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            description: Query parameters for matching.
                            type: object
                          scheme:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                          statPrefix:
                            description: The human readable prefix to use when emitting
                              statistics for this route.
                            type: string
                          uri:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                type: string
                              prefix:
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                type: string
                            type: object
                          withoutHeaders:
                            additionalProperties:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - exact
                                  - required:
                                    - prefix
                                  - required:
                                    - regex
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                              properties:
                                exact:
                                  type: string
                                prefix:
                                  type: string
                                regex:
                                  description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                  type: string
                              type: object
                            description: withoutHeader has the same syntax with the
                              header, but has opposite meaning.
                            type: object
                        type: object
                      type: array
                    mirror:
                      properties:
                        host:
                          description: The name of a service from the service registry.
                          type: string
                        port:
                          description: Specifies the port on the host that is being
                            addressed.
                          properties:
                            number:
                              type: integer
                          type: object
                        subset:
                          description: The name of a subset within the service.
                          type: string
                      type: object
                    mirror_percent:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      nullable: true
                      type: integer
                    mirrorPercent:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      nullable: true
                      type: integer
                    mirrorPercentage:
                      description: Percentage of the traffic to be mirrored by the
                        `mirror` field.
                      properties:
                        value:
                          format: double
                          type: number
                      type: object
                    mirrors:
                      items:
                        properties:
                          destination:
                            description: Destination specifies the target of the mirror
                              operation.
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          percentage:
                            description: Percentage of the traffic to be mirrored
                              by the `destination` field.
                            properties:
                              value:
                                format: double
                                type: number
                            type: object
                        type: object
                      type: array
                    name:
                      description: The name assigned to the route for debugging purposes.
                      type: string
                    redirect:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      oneOf:
                      - not:
                          anyOf:
                          - required:
                            - port
                          - required:
                            - derivePort
                      - required:
                        - port
                      - required:
                        - derivePort
                      properties:
                        authority:
                          type: string
                        derivePort:
                          enum:
                          - FROM_PROTOCOL_DEFAULT
                          - FROM_REQUEST_PORT
                          type: string
                        port:
                          description: On a redirect, overwrite the port portion of
                            the URL with this value.
                          type: integer
                        redirectCode:
                          type: integer
                        scheme:
                          description: On a redirect, overwrite the scheme portion
                            of the URL with this value.
                          type: string
                        uri:
                          type: string
                      type: object
                    retries:
                      description: Retry policy for HTTP requests.
                      properties:
                        attempts:
                          description: Number of retries to be allowed for a given
                            request.
                          format: int32
                          type: integer
                        perTryTimeout:
                          description: Timeout per attempt for a given request, including
                            the initial call and any retries.
                          type: string
                        retryOn:
                          description: Specifies the conditions under which retry
                            takes place.
                          type: string
                        retryRemoteLocalities:
                          description: Flag to specify whether the retries should
                            retry to other localities.
                          nullable: true
                          type: boolean
                      type: object
                    rewrite:
                      description: Rewrite HTTP URIs and Authority headers.
                      properties:
                        authority:
                          description: rewrite the Authority/Host header with this
                            value.
                          type: string
                        uri:
                          type: string
                        uriRegexRewrite:
                          description: rewrite the path portion of the URI with the
                            specified regex.
                          properties:
                            match:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              type: string
                            rewrite:
                              description: The string that should replace into matching
                                portions of original URI.
                              type: string
                          type: object
                      type: object
                    route:
                      description: A HTTP rule can either return a direct_response,
                        redirect or forward (default) traffic.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          headers:
                            properties:
                              request:
                                properties:
                                  add:
                                    additionalProperties:
                                      type: string
                                    type: object
                                  remove:
                                    items:
                                      type: string
                                    type: array
                                  set:
                                    additionalProperties:
                                      type: string
                                    type: object
                                type: object
                              response:
                                properties:
                                  add:
                                    additionalProperties:
                                      type: string
                                    type: object
                                  remove:
                                    items:
                                      type: string
                                    type: array
                                  set:
                                    additionalProperties:
                                      type: string
                                    type: object
                                type: object
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                    timeout:
                      description: Timeout for HTTP requests, default is disabled.
                      type: string
                  type: object
                type: array
              tcp:
                description: An ordered list of route rules for opaque TCP traffic.
                items:
                  properties:
                    match:
                      items:
                        properties:
                          destinationSubnets:
                            description: IPv4 or IPv6 ip addresses of destination
                              with optional subnet.
                            items:
                              type: string
                            type: array
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          port:
                            description: Specifies the port on the host that is being
                              addressed.
                            type: integer
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                          sourceSubnet:
                            description: IPv4 or IPv6 ip address of source with optional
                              subnet.
                            type: string
                        type: object
                      type: array
                    route:
                      description: The destination to which the connection should
                        be forwarded to.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                  type: object
                type: array
              tls:
                items:
                  properties:
                    match:
                      items:
                        properties:
                          destinationSubnets:
                            description: IPv4 or IPv6 ip addresses of destination
                              with optional subnet.
                            items:
                              type: string
                            type: array
                          gateways:
                            description: Names of gateways where the rule should be
                              applied.
                            items:
                              type: string
                            type: array
                          port:
                            description: Specifies the port on the host that is being
                              addressed.
                            type: integer
                          sniHosts:
                            description: SNI (server name indicator) to match on.
                            items:
                              type: string
                            type: array
                          sourceLabels:
                            additionalProperties:
                              type: string
                            type: object
                          sourceNamespace:
                            description: Source namespace constraining the applicability
                              of a rule to workloads in that namespace.
                            type: string
                        type: object
                      type: array
                    route:
                      description: The destination to which the connection should
                        be forwarded to.
                      items:
                        properties:
                          destination:
                            properties:
                              host:
                                description: The name of a service from the service
                                  registry.
                                type: string
                              port:
                                description: Specifies the port on the host that is
                                  being addressed.
                                properties:
                                  number:
                                    type: integer
                                type: object
                              subset:
                                description: The name of a subset within the service.
                                type: string
                            type: object
                          weight:
                            description: Weight specifies the relative proportion
                              of traffic to be forwarded to the destination.
                            format: int32
                            type: integer
                        type: object
                      type: array
                  type: object
                type: array
            type: object
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: destinationrules.networking.istio.io
spec:
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: DestinationRule
    listKind: DestinationRuleList
    plural: destinationrules
    shortNames:
    - dr
    singular: destinationrule
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The name of a service from the service registry
      jsonPath: .spec.host
      name: Host
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha3
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting load balancing, outlier detection,
              etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
            properties:
              exportTo:
                description: A list of namespaces to which this destination rule is
                  exported.
                items:
                  type: string
                type: array
              host:
                description: The name of a service from the service registry.
                type: string
              subsets:
                items:
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      type: object
                    name:
                      description: Name of the subset.
                      type: string
                    trafficPolicy:
                      description: Traffic policies that apply to this subset.
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: Specify if http1.1 connection should
                                    be upgraded to http2 for the associated destination.
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                    probes:
                                      type: integer
                                    time:
                                      type: string
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      minimumRingSize: {}
                                required:
                                - consistentHash
                          - required:
                            - simple
                          - properties:
                              consistentHash:
                                allOf:
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                  - required:
                                    - ringHash
                                  - required:
                                    - maglev
                                properties:
                                  minimumRingSize: {}
                            required:
                            - consistentHash
                          properties:
                            consistentHash:
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: enable locality load balancing, this
                                    is DestinationRule-level and will override mesh
                                    wide settings in entirety.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmupDurationSecs:
                              description: Represents the warmup duration of Service.
                              type: string
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                            maxEjectionPercent:
                              format: int32
                              type: integer
                            minHealthPercent:
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        portLevelSettings:
                          description: Traffic policies specific to individual ports.
                          items:
                            properties:
                              connectionPool:
                                properties:
                                  http:
                                    description: HTTP connection pool settings.
                                    properties:
                                      h2UpgradePolicy:
                                        description: Specify if http1.1 connection
                                          should be upgraded to http2 for the associated
                                          destination.
                                        enum:
                                        - DEFAULT
                                        - DO_NOT_UPGRADE
                                        - UPGRADE
                                        type: string
                                      http1MaxPendingRequests:
                                        format: int32
                                        type: integer
                                      http2MaxRequests:
                                        description: Maximum number of active requests
                                          to a destination.
                                        format: int32
                                        type: integer
                                      idleTimeout:
                                        description: The idle timeout for upstream
                                          connection pool connections.
                                        type: string
                                      maxRequestsPerConnection:
                                        description: Maximum number of requests per
                                          connection to a backend.
                                        format: int32
                                        type: integer
                                      maxRetries:
                                        format: int32
                                        type: integer
                                      useClientProtocol:
                                        description: If set to true, client protocol
                                          will be preserved while initiating connection
                                          to backend.
                                        type: boolean
                                    type: object
                                  tcp:
                                    description: Settings common to both HTTP and
                                      TCP upstream connections.
                                    properties:
                                      connectTimeout:
                                        description: TCP connection timeout.
                                        type: string
                                      maxConnectionDuration:
                                        description: The maximum duration of a connection.
                                        type: string
                                      maxConnections:
                                        description: Maximum number of HTTP1 /TCP
                                          connections to a destination host.
                                        format: int32
                                        type: integer
                                      tcpKeepalive:
                                        description: If set then set SO_KEEPALIVE
                                          on the socket to enable TCP Keepalives.
                                        properties:
                                          interval:
                                            description: The time duration between
                                              keep-alive probes.
                                            type: string
                                          probes:
                                            type: integer
                                          time:
                                            type: string
                                        type: object
                                    type: object
                                type: object
                              loadBalancer:
                                description: Settings controlling the load balancer
                                  algorithms.
                                oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - simple
                                    - properties:
                                        consistentHash:
                                          allOf:
                                          - oneOf:
                                            - not:
                                                anyOf:
                                                - required:
                                                  - httpHeaderName
                                                - required:
                                                  - httpCookie
                                                - required:
                                                  - useSourceIp
                                                - required:
                                                  - httpQueryParameterName
                                            - required:
                                              - httpHeaderName
                                            - required:
                                              - httpCookie
                                            - required:
                                              - useSourceIp
                                            - required:
                                              - httpQueryParameterName
                                          - oneOf:
                                            - not:
                                                anyOf:
                                                - required:
                                                  - ringHash
                                                - required:
                                                  - maglev
                                            - required:
                                              - ringHash
                                            - required:
                                              - maglev
                                          properties:
                                            minimumRingSize: {}
                                      required:
                                      - consistentHash
                                - required:
                                  - simple
                                - properties:
                                    consistentHash:
                                      allOf:
                                      - oneOf:
                                        - not:
                                            anyOf:
                                            - required:
                                              - httpHeaderName
                                            - required:
                                              - httpCookie
                                            - required:
                                              - useSourceIp
                                            - required:
                                              - httpQueryParameterName
                                        - required:
                                          - httpHeaderName
                                        - required:
                                          - httpCookie
                                        - required:
                                          - useSourceIp
                                        - required:
                                          - httpQueryParameterName
                                      - oneOf:
                                        - not:
                                            anyOf:
                                            - required:
                                              - ringHash
                                            - required:
                                              - maglev
                                        - required:
                                          - ringHash
                                        - required:
                                          - maglev
                                      properties:
                                        minimumRingSize: {}
                                  required:
                                  - consistentHash
                                properties:
                                  consistentHash:
                                    properties:
                                      httpCookie:
                                        description: Hash based on HTTP cookie.
                                        properties:
                                          name:
                                            description: Name of the cookie.
                                            type: string
                                          path:
                                            description: Path to set for the cookie.
                                            type: string
                                          ttl:
                                            description: Lifetime of the cookie.
                                            type: string
                                        type: object
                                      httpHeaderName:
                                        description: Hash based on a specific HTTP
                                          header.
                                        type: string
                                      httpQueryParameterName:
                                        description: Hash based on a specific HTTP
                                          query parameter.
                                        type: string
                                      maglev:
                                        description: The Maglev load balancer implements
                                          consistent hashing to backend hosts.
                                        properties:
                                          tableSize:
                                            description: The table size for Maglev
                                              hashing.
                                            type: integer
                                        type: object
                                      minimumRingSize:
                                        description: Deprecated.
                                        type: integer
                                      ringHash:
                                        description: The ring/modulo hash load balancer
                                          implements consistent hashing to backend
                                          hosts.
                                        properties:
                                          minimumRingSize:
                                            type: integer
                                        type: object
                                      useSourceIp:
                                        description: Hash based on the source IP address.
                                        type: boolean
                                    type: object
                                  localityLbSetting:
                                    properties:
                                      distribute:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating locality, '/'
                                                separated, e.g.
                                              type: string
                                            to:
                                              additionalProperties:
                                                type: integer
                                              description: Map of upstream localities
                                                to traffic distribution weights.
                                              type: object
                                          type: object
                                        type: array
                                      enabled:
                                        description: enable locality load balancing,
                                          this is DestinationRule-level and will override
                                          mesh wide settings in entirety.
                                        nullable: true
                                        type: boolean
                                      failover:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating region.
                                              type: string
                                            to:
                                              type: string
                                          type: object
                                        type: array
                                      failoverPriority:
                                        description: failoverPriority is an ordered
                                          list of labels used to sort endpoints to
                                          do priority based load balancing.
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  simple:
                                    enum:
                                    - UNSPECIFIED
                                    - LEAST_CONN
                                    - RANDOM
                                    - PASSTHROUGH
                                    - ROUND_ROBIN
                                    - LEAST_REQUEST
                                    type: string
                                  warmupDurationSecs:
                                    description: Represents the warmup duration of
                                      Service.
                                    type: string
                                type: object
                              outlierDetection:
                                properties:
                                  baseEjectionTime:
                                    description: Minimum ejection duration.
                                    type: string
                                  consecutive5xxErrors:
                                    description: Number of 5xx errors before a host
                                      is ejected from the connection pool.
                                    nullable: true
                                    type: integer
                                  consecutiveErrors:
                                    format: int32
                                    type: integer
                                  consecutiveGatewayErrors:
                                    description: Number of gateway errors before a
                                      host is ejected from the connection pool.
                                    nullable: true
                                    type: integer
                                  consecutiveLocalOriginFailures:
                                    nullable: true
                                    type: integer
                                  interval:
                                    description: Time interval between ejection sweep
                                      analysis.
                                    type: string
                                  maxEjectionPercent:
                                    format: int32
                                    type: integer
                                  minHealthPercent:
                                    format: int32
                                    type: integer
                                  splitExternalLocalOriginErrors:
                                    description: Determines whether to distinguish
                                      local origin failures from external errors.
                                    type: boolean
                                type: object
                              port:
                                properties:
                                  number:
                                    type: integer
                                type: object
                              tls:
                                description: TLS related settings for connections
                                  to the upstream service.
                                properties:
                                  caCertificates:
                                    type: string
                                  clientCertificate:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  credentialName:
                                    type: string
                                  insecureSkipVerify:
                                    nullable: true
                                    type: boolean
                                  mode:
                                    enum:
                                    - DISABLE
                                    - SIMPLE
                                    - MUTUAL
                                    - ISTIO_MUTUAL
                                    type: string
                                  privateKey:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  sni:
                                    description: SNI string to present to the server
                                      during TLS handshake.
                                    type: string
                                  subjectAltNames:
                                    items:
                                      type: string
                                    type: array
                                type: object
                            type: object
                          type: array
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              type: string
                            insecureSkipVerify:
                              nullable: true
                              type: boolean
                            mode:
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              items:
                                type: string
                              type: array
                          type: object
                        tunnel:
                          properties:
                            protocol:
                              description: Specifies which protocol to use for tunneling
                                the downstream connection.
                              type: string
                            targetHost:
                              description: Specifies a host to which the downstream
                                connection is tunneled.
                              type: string
                            targetPort:
                              description: Specifies a port to which the downstream
                                connection is tunneled.
                              type: integer
                          type: object
                      type: object
                  type: object
                type: array
              trafficPolicy:
                properties:
                  connectionPool:
                    properties:
                      http:
                        description: HTTP connection pool settings.
                        properties:
                          h2UpgradePolicy:
                            description: Specify if http1.1 connection should be upgraded
                              to http2 for the associated destination.
                            enum:
                            - DEFAULT
                            - DO_NOT_UPGRADE
                            - UPGRADE
                            type: string
                          http1MaxPendingRequests:
                            format: int32
                            type: integer
                          http2MaxRequests:
                            description: Maximum number of active requests to a destination.
                            format: int32
                            type: integer
                          idleTimeout:
                            description: The idle timeout for upstream connection
                              pool connections.
                            type: string
                          maxRequestsPerConnection:
                            description: Maximum number of requests per connection
                              to a backend.
                            format: int32
                            type: integer
                          maxRetries:
                            format: int32
                            type: integer
                          useClientProtocol:
                            description: If set to true, client protocol will be preserved
                              while initiating connection to backend.
                            type: boolean
                        type: object
                      tcp:
                        description: Settings common to both HTTP and TCP upstream
                          connections.
                        properties:
                          connectTimeout:
                            description: TCP connection timeout.
                            type: string
                          maxConnectionDuration:
                            description: The maximum duration of a connection.
                            type: string
                          maxConnections:
                            description: Maximum number of HTTP1 /TCP connections
                              to a destination host.
                            format: int32
                            type: integer
                          tcpKeepalive:
                            description: If set then set SO_KEEPALIVE on the socket
                              to enable TCP Keepalives.
                            properties:
                              interval:
                                description: The time duration between keep-alive
                                  probes.
                                type: string
                              probes:
                                type: integer
                              time:
                                type: string
                            type: object
                        type: object
                    type: object
                  loadBalancer:
                    description: Settings controlling the load balancer algorithms.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - simple
                        - properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                minimumRingSize: {}
                          required:
                          - consistentHash
                    - required:
                      - simple
                    - properties:
                        consistentHash:
                          allOf:
                          - oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                            - required:
                              - httpHeaderName
                            - required:
                              - httpCookie
                            - required:
                              - useSourceIp
                            - required:
                              - httpQueryParameterName
                          - oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                            - required:
                              - ringHash
                            - required:
                              - maglev
                          properties:
                            minimumRingSize: {}
                      required:
                      - consistentHash
                    properties:
                      consistentHash:
                        properties:
                          httpCookie:
                            description: Hash based on HTTP cookie.
                            properties:
                              name:
                                description: Name of the cookie.
                                type: string
                              path:
                                description: Path to set for the cookie.
                                type: string
                              ttl:
                                description: Lifetime of the cookie.
                                type: string
                            type: object
                          httpHeaderName:
                            description: Hash based on a specific HTTP header.
                            type: string
                          httpQueryParameterName:
                            description: Hash based on a specific HTTP query parameter.
                            type: string
                          maglev:
                            description: The Maglev load balancer implements consistent
                              hashing to backend hosts.
                            properties:
                              tableSize:
                                description: The table size for Maglev hashing.
                                type: integer
                            type: object
                          minimumRingSize:
                            description: Deprecated.
                            type: integer
                          ringHash:
                            description: The ring/modulo hash load balancer implements
                              consistent hashing to backend hosts.
                            properties:
                              minimumRingSize:
                                type: integer
                            type: object
                          useSourceIp:
                            description: Hash based on the source IP address.
                            type: boolean
                        type: object
                      localityLbSetting:
                        properties:
                          distribute:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating locality, '/' separated,
                                    e.g.
                                  type: string
                                to:
                                  additionalProperties:
                                    type: integer
                                  description: Map of upstream localities to traffic
                                    distribution weights.
                                  type: object
                              type: object
                            type: array
                          enabled:
                            description: enable locality load balancing, this is DestinationRule-level
                              and will override mesh wide settings in entirety.
                            nullable: true
                            type: boolean
                          failover:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating region.
                                  type: string
                                to:
                                  type: string
                              type: object
                            type: array
                          failoverPriority:
                            description: failoverPriority is an ordered list of labels
                              used to sort endpoints to do priority based load balancing.
                            items:
                              type: string
                            type: array
                        type: object
                      simple:
                        enum:
                        - UNSPECIFIED
                        - LEAST_CONN
                        - RANDOM
                        - PASSTHROUGH
                        - ROUND_ROBIN
                        - LEAST_REQUEST
                        type: string
                      warmupDurationSecs:
                        description: Represents the warmup duration of Service.
                        type: string
                    type: object
                  outlierDetection:
                    properties:
                      baseEjectionTime:
                        description: Minimum ejection duration.
                        type: string
                      consecutive5xxErrors:
                        description: Number of 5xx errors before a host is ejected
                          from the connection pool.
                        nullable: true
                        type: integer
                      consecutiveErrors:
                        format: int32
                        type: integer
                      consecutiveGatewayErrors:
                        description: Number of gateway errors before a host is ejected
                          from the connection pool.
                        nullable: true
                        type: integer
                      consecutiveLocalOriginFailures:
                        nullable: true
                        type: integer
                      interval:
                        description: Time interval between ejection sweep analysis.
                        type: string
                      maxEjectionPercent:
                        format: int32
                        type: integer
                      minHealthPercent:
                        format: int32
                        type: integer
                      splitExternalLocalOriginErrors:
                        description: Determines whether to distinguish local origin
                          failures from external errors.
                        type: boolean
                    type: object
                  portLevelSettings:
                    description: Traffic policies specific to individual ports.
                    items:
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: Specify if http1.1 connection should
                                    be upgraded to http2 for the associated destination.
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                    probes:
                                      type: integer
                                    time:
                                      type: string
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      minimumRingSize: {}
                                required:
                                - consistentHash
                          - required:
                            - simple
                          - properties:
                              consistentHash:
                                allOf:
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                  - required:
                                    - ringHash
                                  - required:
                                    - maglev
                                properties:
                                  minimumRingSize: {}
                            required:
                            - consistentHash
                          properties:
                            consistentHash:
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: enable locality load balancing, this
                                    is DestinationRule-level and will override mesh
                                    wide settings in entirety.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmupDurationSecs:
                              description: Represents the warmup duration of Service.
                              type: string
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                            maxEjectionPercent:
                              format: int32
                              type: integer
                            minHealthPercent:
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        port:
                          properties:
                            number:
                              type: integer
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              type: string
                            insecureSkipVerify:
                              nullable: true
                              type: boolean
                            mode:
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              items:
                                type: string
                              type: array
                          type: object
                      type: object
                    type: array
                  tls:
                    description: TLS related settings for connections to the upstream
                      service.
                    properties:
                      caCertificates:
                        type: string
                      clientCertificate:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      credentialName:
                        type: string
                      insecureSkipVerify:
                        nullable: true
                        type: boolean
                      mode:
                        enum:
                        - DISABLE
                        - SIMPLE
                        - MUTUAL
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      sni:
                        description: SNI string to present to the server during TLS
                          handshake.
                        type: string
                      subjectAltNames:
                        items:
                          type: string
                        type: array
                    type: object
                  tunnel:
                    properties:
                      protocol:
                        description: Specifies which protocol to use for tunneling
                          the downstream connection.
                        type: string
                      targetHost:
                        description: Specifies a host to which the downstream connection
                          is tunneled.
                        type: string
                      targetPort:
                        description: Specifies a port to which the downstream connection
                          is tunneled.
                        type: integer
                    type: object
                type: object
              workloadSelector:
                properties:
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
            type: object
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: true
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The name of a service from the service registry
      jsonPath: .spec.host
      name: Host
      type: string
    - description: 'CreationTimestamp is a timestamp representing the server time
        when this object was created. It is not guaranteed to be set in happens-before
        order across separate operations. Clients may not set this value. It is represented
        in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
        lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
      jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          spec:
            description: 'Configuration affecting load balancing, outlier detection,
              etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
            properties:
              exportTo:
                description: A list of namespaces to which this destination rule is
                  exported.
                items:
                  type: string
                type: array
              host:
                description: The name of a service from the service registry.
                type: string
              subsets:
                items:
                  properties:
                    labels:
                      additionalProperties:
                        type: string
                      type: object
                    name:
                      description: Name of the subset.
                      type: string
                    trafficPolicy:
                      description: Traffic policies that apply to this subset.
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: Specify if http1.1 connection should
                                    be upgraded to http2 for the associated destination.
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                    probes:
                                      type: integer
                                    time:
                                      type: string
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      minimumRingSize: {}
                                required:
                                - consistentHash
                          - required:
                            - simple
                          - properties:
                              consistentHash:
                                allOf:
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                  - required:
                                    - ringHash
                                  - required:
                                    - maglev
                                properties:
                                  minimumRingSize: {}
                            required:
                            - consistentHash
                          properties:
                            consistentHash:
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: enable locality load balancing, this
                                    is DestinationRule-level and will override mesh
                                    wide settings in entirety.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmupDurationSecs:
                              description: Represents the warmup duration of Service.
                              type: string
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                            maxEjectionPercent:
                              format: int32
                              type: integer
                            minHealthPercent:
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        portLevelSettings:
                          description: Traffic policies specific to individual ports.
                          items:
                            properties:
                              connectionPool:
                                properties:
                                  http:
                                    description: HTTP connection pool settings.
                                    properties:
                                      h2UpgradePolicy:
                                        description: Specify if http1.1 connection
                                          should be upgraded to http2 for the associated
                                          destination.
                                        enum:
                                        - DEFAULT
                                        - DO_NOT_UPGRADE
                                        - UPGRADE
                                        type: string
                                      http1MaxPendingRequests:
                                        format: int32
                                        type: integer
                                      http2MaxRequests:
                                        description: Maximum number of active requests
                                          to a destination.
                                        format: int32
                                        type: integer
                                      idleTimeout:
                                        description: The idle timeout for upstream
                                          connection pool connections.
                                        type: string
                                      maxRequestsPerConnection:
                                        description: Maximum number of requests per
                                          connection to a backend.
                                        format: int32
                                        type: integer
                                      maxRetries:
                                        format: int32
                                        type: integer
                                      useClientProtocol:
                                        description: If set to true, client protocol
                                          will be preserved while initiating connection
                                          to backend.
                                        type: boolean
                                    type: object
                                  tcp:
                                    description: Settings common to both HTTP and
                                      TCP upstream connections.
                                    properties:
                                      connectTimeout:
                                        description: TCP connection timeout.
                                        type: string
                                      maxConnectionDuration:
                                        description: The maximum duration of a connection.
                                        type: string
                                      maxConnections:
                                        description: Maximum number of HTTP1 /TCP
                                          connections to a destination host.
                                        format: int32
                                        type: integer
                                      tcpKeepalive:
                                        description: If set then set SO_KEEPALIVE
                                          on the socket to enable TCP Keepalives.
                                        properties:
                                          interval:
                                            description: The time duration between
                                              keep-alive probes.
                                            type: string
                                          probes:
                                            type: integer
                                          time:
                                            type: string
                                        type: object
                                    type: object
                                type: object
                              loadBalancer:
                                description: Settings controlling the load balancer
                                  algorithms.
                                oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - simple
                                    - properties:
                                        consistentHash:
                                          allOf:
                                          - oneOf:
                                            - not:
                                                anyOf:
                                                - required:
                                                  - httpHeaderName
                                                - required:
                                                  - httpCookie
                                                - required:
                                                  - useSourceIp
                                                - required:
                                                  - httpQueryParameterName
                                            - required:
                                              - httpHeaderName
                                            - required:
                                              - httpCookie
                                            - required:
                                              - useSourceIp
                                            - required:
                                              - httpQueryParameterName
                                          - oneOf:
                                            - not:
                                                anyOf:
                                                - required:
                                                  - ringHash
                                                - required:
                                                  - maglev
                                            - required:
                                              - ringHash
                                            - required:
                                              - maglev
                                          properties:
                                            minimumRingSize: {}
                                      required:
                                      - consistentHash
                                - required:
                                  - simple
                                - properties:
                                    consistentHash:
                                      allOf:
                                      - oneOf:
                                        - not:
                                            anyOf:
                                            - required:
                                              - httpHeaderName
                                            - required:
                                              - httpCookie
                                            - required:
                                              - useSourceIp
                                            - required:
                                              - httpQueryParameterName
                                        - required:
                                          - httpHeaderName
                                        - required:
                                          - httpCookie
                                        - required:
                                          - useSourceIp
                                        - required:
                                          - httpQueryParameterName
                                      - oneOf:
                                        - not:
                                            anyOf:
                                            - required:
                                              - ringHash
                                            - required:
                                              - maglev
                                        - required:
                                          - ringHash
                                        - required:
                                          - maglev
                                      properties:
                                        minimumRingSize: {}
                                  required:
                                  - consistentHash
                                properties:
                                  consistentHash:
                                    properties:
                                      httpCookie:
                                        description: Hash based on HTTP cookie.
                                        properties:
                                          name:
                                            description: Name of the cookie.
                                            type: string
                                          path:
                                            description: Path to set for the cookie.
                                            type: string
                                          ttl:
                                            description: Lifetime of the cookie.
                                            type: string
                                        type: object
                                      httpHeaderName:
                                        description: Hash based on a specific HTTP
                                          header.
                                        type: string
                                      httpQueryParameterName:
                                        description: Hash based on a specific HTTP
                                          query parameter.
                                        type: string
                                      maglev:
                                        description: The Maglev load balancer implements
                                          consistent hashing to backend hosts.
                                        properties:
                                          tableSize:
                                            description: The table size for Maglev
                                              hashing.
                                            type: integer
                                        type: object
                                      minimumRingSize:
                                        description: Deprecated.
                                        type: integer
                                      ringHash:
                                        description: The ring/modulo hash load balancer
                                          implements consistent hashing to backend
                                          hosts.
                                        properties:
                                          minimumRingSize:
                                            type: integer
                                        type: object
                                      useSourceIp:
                                        description: Hash based on the source IP address.
                                        type: boolean
                                    type: object
                                  localityLbSetting:
                                    properties:
                                      distribute:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating locality, '/'
                                                separated, e.g.
                                              type: string
                                            to:
                                              additionalProperties:
                                                type: integer
                                              description: Map of upstream localities
                                                to traffic distribution weights.
                                              type: object
                                          type: object
                                        type: array
                                      enabled:
                                        description: enable locality load balancing,
                                          this is DestinationRule-level and will override
                                          mesh wide settings in entirety.
                                        nullable: true
                                        type: boolean
                                      failover:
                                        description: 'Optional: only one of distribute,
                                          failover or failoverPriority can be set.'
                                        items:
                                          properties:
                                            from:
                                              description: Originating region.
                                              type: string
                                            to:
                                              type: string
                                          type: object
                                        type: array
                                      failoverPriority:
                                        description: failoverPriority is an ordered
                                          list of labels used to sort endpoints to
                                          do priority based load balancing.
                                        items:
                                          type: string
                                        type: array
                                    type: object
                                  simple:
                                    enum:
                                    - UNSPECIFIED
                                    - LEAST_CONN
                                    - RANDOM
                                    - PASSTHROUGH
                                    - ROUND_ROBIN
                                    - LEAST_REQUEST
                                    type: string
                                  warmupDurationSecs:
                                    description: Represents the warmup duration of
                                      Service.
                                    type: string
                                type: object
                              outlierDetection:
                                properties:
                                  baseEjectionTime:
                                    description: Minimum ejection duration.
                                    type: string
                                  consecutive5xxErrors:
                                    description: Number of 5xx errors before a host
                                      is ejected from the connection pool.
                                    nullable: true
                                    type: integer
                                  consecutiveErrors:
                                    format: int32
                                    type: integer
                                  consecutiveGatewayErrors:
                                    description: Number of gateway errors before a
                                      host is ejected from the connection pool.
                                    nullable: true
                                    type: integer
                                  consecutiveLocalOriginFailures:
                                    nullable: true
                                    type: integer
                                  interval:
                                    description: Time interval between ejection sweep
                                      analysis.
                                    type: string
                                  maxEjectionPercent:
                                    format: int32
                                    type: integer
                                  minHealthPercent:
                                    format: int32
                                    type: integer
                                  splitExternalLocalOriginErrors:
                                    description: Determines whether to distinguish
                                      local origin failures from external errors.
                                    type: boolean
                                type: object
                              port:
                                properties:
                                  number:
                                    type: integer
                                type: object
                              tls:
                                description: TLS related settings for connections
                                  to the upstream service.
                                properties:
                                  caCertificates:
                                    type: string
                                  clientCertificate:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  credentialName:
                                    type: string
                                  insecureSkipVerify:
                                    nullable: true
                                    type: boolean
                                  mode:
                                    enum:
                                    - DISABLE
                                    - SIMPLE
                                    - MUTUAL
                                    - ISTIO_MUTUAL
                                    type: string
                                  privateKey:
                                    description: REQUIRED if mode is `MUTUAL`.
                                    type: string
                                  sni:
                                    description: SNI string to present to the server
                                      during TLS handshake.
                                    type: string
                                  subjectAltNames:
                                    items:
                                      type: string
                                    type: array
                                type: object
                            type: object
                          type: array
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              type: string
                            insecureSkipVerify:
                              nullable: true
                              type: boolean
                            mode:
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              items:
                                type: string
                              type: array
                          type: object
                        tunnel:
                          properties:
                            protocol:
                              description: Specifies which protocol to use for tunneling
                                the downstream connection.
                              type: string
                            targetHost:
                              description: Specifies a host to which the downstream
                                connection is tunneled.
                              type: string
                            targetPort:
                              description: Specifies a port to which the downstream
                                connection is tunneled.
                              type: integer
                          type: object
                      type: object
                  type: object
                type: array
              trafficPolicy:
                properties:
                  connectionPool:
                    properties:
                      http:
                        description: HTTP connection pool settings.
                        properties:
                          h2UpgradePolicy:
                            description: Specify if http1.1 connection should be upgraded
                              to http2 for the associated destination.
                            enum:
                            - DEFAULT
                            - DO_NOT_UPGRADE
                            - UPGRADE
                            type: string
                          http1MaxPendingRequests:
                            format: int32
                            type: integer
                          http2MaxRequests:
                            description: Maximum number of active requests to a destination.
                            format: int32
                            type: integer
                          idleTimeout:
                            description: The idle timeout for upstream connection
                              pool connections.
                            type: string
                          maxRequestsPerConnection:
                            description: Maximum number of requests per connection
                              to a backend.
                            format: int32
                            type: integer
                          maxRetries:
                            format: int32
                            type: integer
                          useClientProtocol:
                            description: If set to true, client protocol will be preserved
                              while initiating connection to backend.
                            type: boolean
                        type: object
                      tcp:
                        description: Settings common to both HTTP and TCP upstream
                          connections.
                        properties:
                          connectTimeout:
                            description: TCP connection timeout.
                            type: string
                          maxConnectionDuration:
                            description: The maximum duration of a connection.
                            type: string
                          maxConnections:
                            description: Maximum number of HTTP1 /TCP connections
                              to a destination host.
                            format: int32
                            type: integer
                          tcpKeepalive:
                            description: If set then set SO_KEEPALIVE on the socket
                              to enable TCP Keepalives.
                            properties:
                              interval:
                                description: The time duration between keep-alive
                                  probes.
                                type: string
                              probes:
                                type: integer
                              time:
                                type: string
                            type: object
                        type: object
                    type: object
                  loadBalancer:
                    description: Settings controlling the load balancer algorithms.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - simple
                        - properties:
                            consistentHash:
                              allOf:
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                              - oneOf:
                                - not:
                                    anyOf:
                                    - required:
                                      - ringHash
                                    - required:
                                      - maglev
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                              properties:
                                minimumRingSize: {}
                          required:
                          - consistentHash
                    - required:
                      - simple
                    - properties:
                        consistentHash:
                          allOf:
                          - oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                            - required:
                              - httpHeaderName
                            - required:
                              - httpCookie
                            - required:
                              - useSourceIp
                            - required:
                              - httpQueryParameterName
                          - oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - ringHash
                                - required:
                                  - maglev
                            - required:
                              - ringHash
                            - required:
                              - maglev
                          properties:
                            minimumRingSize: {}
                      required:
                      - consistentHash
                    properties:
                      consistentHash:
                        properties:
                          httpCookie:
                            description: Hash based on HTTP cookie.
                            properties:
                              name:
                                description: Name of the cookie.
                                type: string
                              path:
                                description: Path to set for the cookie.
                                type: string
                              ttl:
                                description: Lifetime of the cookie.
                                type: string
                            type: object
                          httpHeaderName:
                            description: Hash based on a specific HTTP header.
                            type: string
                          httpQueryParameterName:
                            description: Hash based on a specific HTTP query parameter.
                            type: string
                          maglev:
                            description: The Maglev load balancer implements consistent
                              hashing to backend hosts.
                            properties:
                              tableSize:
                                description: The table size for Maglev hashing.
                                type: integer
                            type: object
                          minimumRingSize:
                            description: Deprecated.
                            type: integer
                          ringHash:
                            description: The ring/modulo hash load balancer implements
                              consistent hashing to backend hosts.
                            properties:
                              minimumRingSize:
                                type: integer
                            type: object
                          useSourceIp:
                            description: Hash based on the source IP address.
                            type: boolean
                        type: object
                      localityLbSetting:
                        properties:
                          distribute:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating locality, '/' separated,
                                    e.g.
                                  type: string
                                to:
                                  additionalProperties:
                                    type: integer
                                  description: Map of upstream localities to traffic
                                    distribution weights.
                                  type: object
                              type: object
                            type: array
                          enabled:
                            description: enable locality load balancing, this is DestinationRule-level
                              and will override mesh wide settings in entirety.
                            nullable: true
                            type: boolean
                          failover:
                            description: 'Optional: only one of distribute, failover
                              or failoverPriority can be set.'
                            items:
                              properties:
                                from:
                                  description: Originating region.
                                  type: string
                                to:
                                  type: string
                              type: object
                            type: array
                          failoverPriority:
                            description: failoverPriority is an ordered list of labels
                              used to sort endpoints to do priority based load balancing.
                            items:
                              type: string
                            type: array
                        type: object
                      simple:
                        enum:
                        - UNSPECIFIED
                        - LEAST_CONN
                        - RANDOM
                        - PASSTHROUGH
                        - ROUND_ROBIN
                        - LEAST_REQUEST
                        type: string
                      warmupDurationSecs:
                        description: Represents the warmup duration of Service.
                        type: string
                    type: object
                  outlierDetection:
                    properties:
                      baseEjectionTime:
                        description: Minimum ejection duration.
                        type: string
                      consecutive5xxErrors:
                        description: Number of 5xx errors before a host is ejected
                          from the connection pool.
                        nullable: true
                        type: integer
                      consecutiveErrors:
                        format: int32
                        type: integer
                      consecutiveGatewayErrors:
                        description: Number of gateway errors before a host is ejected
                          from the connection pool.
                        nullable: true
                        type: integer
                      consecutiveLocalOriginFailures:
                        nullable: true
                        type: integer
                      interval:
                        description: Time interval between ejection sweep analysis.
                        type: string
                      maxEjectionPercent:
                        format: int32
                        type: integer
                      minHealthPercent:
                        format: int32
                        type: integer
                      splitExternalLocalOriginErrors:
                        description: Determines whether to distinguish local origin
                          failures from external errors.
                        type: boolean
                    type: object
                  portLevelSettings:
                    description: Traffic policies specific to individual ports.
                    items:
                      properties:
                        connectionPool:
                          properties:
                            http:
                              description: HTTP connection pool settings.
                              properties:
                                h2UpgradePolicy:
                                  description: Specify if http1.1 connection should
                                    be upgraded to http2 for the associated destination.
                                  enum:
                                  - DEFAULT
                                  - DO_NOT_UPGRADE
                                  - UPGRADE
                                  type: string
                                http1MaxPendingRequests:
                                  format: int32
                                  type: integer
                                http2MaxRequests:
                                  description: Maximum number of active requests to
                                    a destination.
                                  format: int32
                                  type: integer
                                idleTimeout:
                                  description: The idle timeout for upstream connection
                                    pool connections.
                                  type: string
                                maxRequestsPerConnection:
                                  description: Maximum number of requests per connection
                                    to a backend.
                                  format: int32
                                  type: integer
                                maxRetries:
                                  format: int32
                                  type: integer
                                useClientProtocol:
                                  description: If set to true, client protocol will
                                    be preserved while initiating connection to backend.
                                  type: boolean
                              type: object
                            tcp:
                              description: Settings common to both HTTP and TCP upstream
                                connections.
                              properties:
                                connectTimeout:
                                  description: TCP connection timeout.
                                  type: string
                                maxConnectionDuration:
                                  description: The maximum duration of a connection.
                                  type: string
                                maxConnections:
                                  description: Maximum number of HTTP1 /TCP connections
                                    to a destination host.
                                  format: int32
                                  type: integer
                                tcpKeepalive:
                                  description: If set then set SO_KEEPALIVE on the
                                    socket to enable TCP Keepalives.
                                  properties:
                                    interval:
                                      description: The time duration between keep-alive
                                        probes.
                                      type: string
                                    probes:
                                      type: integer
                                    time:
                                      type: string
                                  type: object
                              type: object
                          type: object
                        loadBalancer:
                          description: Settings controlling the load balancer algorithms.
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - simple
                              - properties:
                                  consistentHash:
                                    allOf:
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - httpHeaderName
                                          - required:
                                            - httpCookie
                                          - required:
                                            - useSourceIp
                                          - required:
                                            - httpQueryParameterName
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                    - oneOf:
                                      - not:
                                          anyOf:
                                          - required:
                                            - ringHash
                                          - required:
                                            - maglev
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                    properties:
                                      minimumRingSize: {}
                                required:
                                - consistentHash
                          - required:
                            - simple
                          - properties:
                              consistentHash:
                                allOf:
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                                - oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - ringHash
                                      - required:
                                        - maglev
                                  - required:
                                    - ringHash
                                  - required:
                                    - maglev
                                properties:
                                  minimumRingSize: {}
                            required:
                            - consistentHash
                          properties:
                            consistentHash:
                              properties:
                                httpCookie:
                                  description: Hash based on HTTP cookie.
                                  properties:
                                    name:
                                      description: Name of the cookie.
                                      type: string
                                    path:
                                      description: Path to set for the cookie.
                                      type: string
                                    ttl:
                                      description: Lifetime of the cookie.
                                      type: string
                                  type: object
                                httpHeaderName:
                                  description: Hash based on a specific HTTP header.
                                  type: string
                                httpQueryParameterName:
                                  description: Hash based on a specific HTTP query
                                    parameter.
                                  type: string
                                maglev:
                                  description: The Maglev load balancer implements
                                    consistent hashing to backend hosts.
                                  properties:
                                    tableSize:
                                      description: The table size for Maglev hashing.
                                      type: integer
                                  type: object
                                minimumRingSize:
                                  description: Deprecated.
                                  type: integer
                                ringHash:
                                  description: The ring/modulo hash load balancer
                                    implements consistent hashing to backend hosts.
                                  properties:
                                    minimumRingSize:
                                      type: integer
                                  type: object
                                useSourceIp:
                                  description: Hash based on the source IP address.
                                  type: boolean
                              type: object
                            localityLbSetting:
                              properties:
                                distribute:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating locality, '/' separated,
                                          e.g.
                                        type: string
                                      to:
                                        additionalProperties:
                                          type: integer
                                        description: Map of upstream localities to
                                          traffic distribution weights.
                                        type: object
                                    type: object
                                  type: array
                                enabled:
                                  description: enable locality load balancing, this
                                    is DestinationRule-level and will override mesh
                                    wide settings in entirety.
                                  nullable: true
                                  type: boolean
                                failover:
                                  description: 'Optional: only one of distribute,
                                    failover or failoverPriority can be set.'
                                  items:
                                    properties:
                                      from:
                                        description: Originating region.
                                        type: string
                                      to:
                                        type: string
                                    type: object
                                  type: array
                                failoverPriority:
                                  description: failoverPriority is an ordered list
                                    of labels used to sort endpoints to do priority
                                    based load balancing.
                                  items:
                                    type: string
                                  type: array
                              type: object
                            simple:
                              enum:
                              - UNSPECIFIED
                              - LEAST_CONN
                              - RANDOM
                              - PASSTHROUGH
                              - ROUND_ROBIN
                              - LEAST_REQUEST
                              type: string
                            warmupDurationSecs:
                              description: Represents the warmup duration of Service.
                              type: string
                          type: object
                        outlierDetection:
                          properties:
                            baseEjectionTime:
                              description: Minimum ejection duration.
                              type: string
                            consecutive5xxErrors:
                              description: Number of 5xx errors before a host is ejected
                                from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveErrors:
                              format: int32
                              type: integer
                            consecutiveGatewayErrors:
                              description: Number of gateway errors before a host
                                is ejected from the connection pool.
                              nullable: true
                              type: integer
                            consecutiveLocalOriginFailures:
                              nullable: true
                              type: integer
                            interval:
                              description: Time interval between ejection sweep analysis.
                              type: string
                            maxEjectionPercent:
                              format: int32
                              type: integer
                            minHealthPercent:
                              format: int32
                              type: integer
                            splitExternalLocalOriginErrors:
                              description: Determines whether to distinguish local
                                origin failures from external errors.
                              type: boolean
                          type: object
                        port:
                          properties:
                            number:
                              type: integer
                          type: object
                        tls:
                          description: TLS related settings for connections to the
                            upstream service.
                          properties:
                            caCertificates:
                              type: string
                            clientCertificate:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            credentialName:
                              type: string
                            insecureSkipVerify:
                              nullable: true
                              type: boolean
                            mode:
                              enum:
                              - DISABLE
                              - SIMPLE
                              - MUTUAL
                              - ISTIO_MUTUAL
                              type: string
                            privateKey:
                              description: REQUIRED if mode is `MUTUAL`.
                              type: string
                            sni:
                              description: SNI string to present to the server during
                                TLS handshake.
                              type: string
                            subjectAltNames:
                              items:
                                type: string
                              type: array
                          type: object
                      type: object
                    type: array
                  tls:
                    description: TLS related settings for connections to the upstream
                      service.
                    properties:
                      caCertificates:
                        type: string
                      clientCertificate:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      credentialName:
                        type: string
                      insecureSkipVerify:
                        nullable: true
                        type: boolean
                      mode:
                        enum:
                        - DISABLE
                        - SIMPLE
                        - MUTUAL
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `MUTUAL`.
                        type: string
                      sni:
                        description: SNI string to present to the server during TLS
                          handshake.
                        type: string
                      subjectAltNames:
                        items:
                          type: string
                        type: array
                    type: object
                  tunnel:
                    properties:
                      protocol:
                        description: Specifies which protocol to use for tunneling
                          the downstream connection.
                        type: string
                      targetHost:
                        description: Specifies a host to which the downstream connection
                          is tunneled.
                        type: string
                      targetPort:
                        description: Specifies a port to which the downstream connection
                          is tunneled.
                        type: integer
                    type: object
                type: object
              workloadSelector:
                properties:
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
            type: object
          status:
            type: object
            x-kubernetes-preserve-unknown-fields: true
        type: object
    served: true
    storage: false
    subresources:
      status: {}