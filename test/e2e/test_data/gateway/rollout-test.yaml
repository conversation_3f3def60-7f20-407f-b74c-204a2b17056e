apiVersion: rollouts.kruise.io/v1alpha1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  objectRef:
    workloadRef:
      apiVersion: apps/v1
      kind: Deployment
      name: echoserver
  strategy:
    canary:
      steps:
      - weight: 20
        pause: {}
      - weight: 40
        pause: {duration: 10}
      - weight: 60
        pause: {duration: 10}
      - weight: 80
        pause: {duration: 10}
      - weight: 100
      trafficRoutings:
      - service: echoserver
        gateway:
          httpRouteName: echoserver
