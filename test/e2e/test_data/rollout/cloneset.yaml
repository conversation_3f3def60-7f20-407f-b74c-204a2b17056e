apiVersion: apps.kruise.io/v1alpha1
kind: CloneSet
metadata:
  name: echoserver
  labels:
    app: echoserver
  annotations:
    rollouts.kruise.io/e2e-test-sample: "true"
spec:
  replicas: 5
  updateStrategy:
    maxUnavailable: 0
    maxSurge: 1
  selector:
    matchLabels:
      app: echoserver
  template:
    metadata:
      labels:
        app: echoserver
    spec:
      containers:
        - name: echoserver
          image: cilium/echoserver:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: PORT
              value: '8080'
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              value: version1

