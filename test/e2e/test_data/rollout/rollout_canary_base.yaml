# we recommend that new test cases or modifications to existing test cases should
# use v1beta1 Rollout, eg. use rollout_v1beta1_canary_base.yaml
apiVersion: rollouts.kruise.io/v1alpha1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  objectRef:
    workloadRef:
      apiVersion: apps/v1
      kind: Deployment
      name: echoserver
  strategy:
    canary:
      steps:
      - weight: 20
        pause: {}
      - weight: 40
        pause: {duration: 10}
      - weight: 60
        pause: {duration: 10}
      - weight: 80
        pause: {duration: 10}
      - weight: 100
        pause: {duration: 0}
      trafficRoutings:
      - service: echoserver
        ingress:
          classType: nginx
          name: echoserver
