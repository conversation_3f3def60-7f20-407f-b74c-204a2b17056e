apiVersion: apps.kruise.io/v1beta1
kind: StatefulSet
metadata:
  name: echoserver
  labels:
    apps: echoserver
spec:
  selector:
    matchLabels:
      app: echoserver
  serviceName: headless-service
  replicas: 5
  template:
    metadata:
      labels:
        app: echoserver
    spec:
      containers:
        - name: echoserver
          image: cilium/echoserver:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: PORT
              value: '8080'
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODE_NAME
              value: version1
