apiVersion: rollouts.kruise.io/v1beta1 # we use v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: echoserver
  strategy:
    canary:
      enableExtraWorkloadForCanary: true
      steps:
      - traffic: 20%
        replicas: 20%
        pause: {}
      - traffic: 40%
        replicas: 40%
        pause: {duration: 10}
      - traffic: 60%
        replicas: 60%
        pause: {duration: 10}
      - traffic: 80%
        replicas: 80%
        pause: {duration: 10}
      - traffic: 100%
        replicas: 100%
        pause: {duration: 0}
      trafficRoutings:
      - service: echoserver
        ingress:
          classType: nginx
          name: echoserver
