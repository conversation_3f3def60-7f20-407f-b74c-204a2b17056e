---
title: v1beta1-apis-proposal

authors:
- "@zmberg"

creation-date: 2023-11-07

---

## Motivation

The Kruise Rollout project has been stable for a year, recently we plan to upgrade the apis from v1alpha1 to v1beta1 and optimize some of the fields in response to past questions and community feedback,
this proposal will organize the v1beta1 apis and discuss it with the community.

## Proposal
To make it easier to understand, I'm going to introduce the v1beta1 field from 6 scenario.

### Canary Release
```
apiVersion: rollouts.kruise.io/v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workload-demo
  strategy:
    canary:
      # If true, then it will create new deployment for canary, such as: workload-demo-canary.
      # When user verifies that the canary version is OK, we will remove the canary deployment and release the deployment workload-demo in full.
      # Current only support k8s native deployment
      enableExtraWorkloadForCanary: true
      steps:
      - trafficWeight: 20%
        desiredReplicas: 2
      trafficRoutings:
      - service: service-demo
        ingress:
          classType: nginx
          name: ingress-demo
```

### A/B Testing Release
```
apiVersion: rollouts.kruise.io/v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workload-demo
  strategy:
    canary:
      enableExtraWorkloadForCanary: true
      steps:
      - desiredReplicas: 2
        trafficMatches:
        - headers:
          - name: user-agent
            type: Exact
            value: pc
      trafficRoutings:
      - service: service-demo
        ingress:
          classType: nginx
          name: ingress-demo
```

### Only Batch Release
```
apiVersion: rollouts.kruise.io/v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workload-demo
  strategy:
    canary:
      steps:
      - desiredReplicas: 1
      - desiredReplicas: 10%
        # After desiredReplicas Pods are ready, sleep 60 and continue to release later batches.
        # If you don't configure it, manual confirmation is required by default.
        pause: {duration: 60}
      - desiredReplicas: 30%
        pause: {duration: 60}
      - desiredReplicas: 60%
        pause: {duration: 60}
      - desiredReplicas: 100%
        pause: {duration: 60}
```

### Batch Release + Traffic Weight
```
apiVersion: rollouts.kruise.io/v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workload-demo
  strategy:
    canary:
      steps:
      - trafficWeight: 5%
        desiredReplicas: 2
      - desiredReplicas: 30%
      - desiredReplicas: 60%
      - desiredReplicas: 100%
      trafficRoutings:
      - service: service-demo
        ingress:
          classType: nginx
          name: ingress-demo
```
### Batch Release + Traffic A/B Testing
```
apiVersion: rollouts.kruise.io/v1beta1
kind: Rollout
metadata:
  name: rollouts-demo
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: workload-demo
  strategy:
    canary:
      steps:
      - trafficMatches:
        - headers:
          - name: user-agent
            type: Exact
            value: pc
        desiredReplicas: 2
      - desiredReplicas: 30%
      - desiredReplicas: 60%
      - desiredReplicas: 100%
      trafficRoutings:
      - service: service-demo
        ingress:
          classType: nginx
          name: ingress-demo
```
### End-to-End progressive delivery for microservice application
```
apiVersion: rollouts.kruise.io/v1alpha1
kind: TrafficRouting
metadata:
  name: mse-traffic
spec:
  objectRef:
  - service: spring-cloud-a
    ingress:
      classType: mse
      name: spring-cloud-a
  strategy:
    matches:
    - headers:
      - type: Exact
        name: User-Agent
        value: xiaoming
    # http request via ingress, and add header[x-mse-tag]=gray
    # for mse or istio routing the gray traffic to gray application
    requestHeaderModifier:
      set:
      - name: x-mse-tag
        value: gray
---
apiVersion: rollouts.kruise.io/v1alpha1
kind: Rollout
metadata:
  name: rollout-a
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: spring-cloud-a
  strategy:
    canary:
      enableExtraWorkloadForCanary: true
      # Type TrafficRouting's name
      trafficRoutingRef: mse-traffic
      steps:
      - desiredReplicas: 1
      # patch pod template metadata to canary workload
      # current only support deployment, and when enableExtraWorkloadForCanary=true
      patchPodTemplateMetadata:
        labels:
          alicloud.service.tag: gray
          opensergo.io/canary-gray: gray
---
apiVersion: rollouts.kruise.io/v1alpha1
kind: Rollout
metadata:
  name: rollout-a
spec:
  workloadRef:
    apiVersion: apps/v1
    kind: Deployment
    name: spring-cloud-a
  strategy:
    canary:
      enableExtraWorkloadForCanary: true
      # Type TrafficRouting's name
      trafficRoutingRef: mse-traffic
      steps:
      - desiredReplicas: 1
      # patch pod template metadata to canary workload
      patchPodTemplateMetadata:
        labels:
          alicloud.service.tag: gray
          opensergo.io/canary-gray: gray
```
