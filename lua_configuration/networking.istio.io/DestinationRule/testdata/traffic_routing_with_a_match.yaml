trafficRouting: 
  apiVersion: rollouts.kruise.io/v1alpha1
  kind: TrafficRouting
  metadata:
    name: tr-demo
  spec:
    strategy:
      matches:
        - headers:
          - type: Exact
            name: version
            value: canary
    objectRef:
    - service: svc-demo
      customNetworkRefs:
      - apiVersion: networking.istio.io/v1beta1
        kind: DestinationRule
        name: ds-demo
original:
  apiVersion: networking.istio.io/v1beta1
  kind: DestinationRule
  metadata:
    name: ds-demo
  spec:
    host: svc-demo
    trafficPolicy:
      loadBalancer:
        simple: ROUND_ROBIN
    subsets:
      - labels:
          version: base
        name: version-base
expected:
  - apiVersion: networking.istio.io/v1beta1
    kind: DestinationRule
    metadata:
      name: ds-demo
    spec:
      host: svc-demo
      trafficPolicy:
        loadBalancer:
          simple: ROUND_ROBIN
      subsets:
        - labels:
            version: base
          name: version-base
        - labels:
            istio.service.tag: gray
          name: canary