trafficRouting:
  apiVersion: rollouts.kruise.io/v1alpha1
  kind: TrafficRouting
  metadata:
    name: tr-demo
  spec:
    strategy:
      matches:
      - headers:
        - type: Exact
          name: user-agent
          value: pc
        - type: RegularExpression
          name: name
          value: ".*demo"
      requestHeaderModifier:
        set:
        - name: "header-foo"
          value: "bar"
    objectRef:
    - service: svc-demo
      customNetworkRefs:
      - apiVersion: networking.istio.io/v1alpha3
        kind: VirtualService
        name: vs-demo
original:
  apiVersion: networking.istio.io/v1alpha3
  kind: VirtualService
  metadata:
    name: vs-demo
  spec:
    hosts:
    - "*"
    gateways:
    - nginx-gateway
    http:
    - route:
      - destination:
          host: svc-demo
          subset: base
expected:
  - apiVersion: networking.istio.io/v1alpha3
    kind: VirtualService
    metadata:
      name: vs-demo
    spec:
      hosts:
      - "*"
      gateways:
      - nginx-gateway
      http:
      - match:
        - headers:
            user-agent:
              exact: pc
            name:
              regex: .*demo
        headers:
          request:
            set:
              header-foo: bar
        route:
        - destination:
            host: svc-demo
            subset: canary
      - route:
        - destination:
            host: svc-demo
            subset: base
