name: License
on:
  push:
    branches:
      - master
      - release-*
  workflow_dispatch: {}
  pull_request:
    branches:
      - master
      - release-*

# Declare default permissions as read only.
permissions: read-all

jobs:
  license_check:
    runs-on: ubuntu-latest
    name: Check for unapproved licenses
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.6
      - name: Install dependencies
        run: gem install license_finder
      - name: Run tests
        run: license_finder --decisions_file .license/dependency_decisions.yml