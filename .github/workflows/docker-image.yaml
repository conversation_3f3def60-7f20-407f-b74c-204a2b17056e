name: Docker Image CI

on:
  workflow_dispatch:

# Declare default permissions as read only.
permissions: read-all

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.HUB_KRUISE }}
      - name: Build the Docker image
        run: |
          docker buildx create --use --platform=linux/amd64,linux/arm64,linux/ppc64le --name multi-platform-builder
          docker buildx ls
          IMG=openkruise/kruise-rollout:${{ github.ref_name }} make docker-multiarch
