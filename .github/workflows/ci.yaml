name: CI

on:
  push:
    branches:
      - master
      - release-*
  pull_request: {}
  workflow_dispatch: {}

env:
  # Common versions
  GO_VERSION: '1.20'
  GOLANGCI_VERSION: 'v2.1'

jobs:

  golangci-lint:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true
      - name: Setup Go
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Cache Go Dependencies
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-
      - name: Code generate
        run: |
          make generate
      - name: Lint golang code
        uses: golangci/golangci-lint-action@4afd733a84b1f43292c63897423277bb7f4313a9 # v8.0.0
        with:
          version: ${{ env.GOLANGCI_VERSION }}
          args: --verbose
          skip-pkg-cache: true
          mod: readonly

  unit-tests:
    runs-on: ubuntu-24.04
    steps:
      - uses:  actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true
      - name: Fetch History
        run: git fetch --prune --unshallow
      - name: Setup Go
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Cache Go Dependencies
        uses: actions/cache@1bd1e32a3bdc45362d1e726936510720a7c30a57 # v4.2.0
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: ${{ runner.os }}-go-
      - name: Run Unit Tests
        run: |
          make test
          [[ -z $(git status -s) ]] || (printf "Existing modified/untracked files.\nPlease run \"make generate manifests\" and push again.\n"; exit 1)
      - name: Publish Unit Test Coverage
        uses: codecov/codecov-action@18283e04ce6e62d37312384ff67231eb8fd56d24 # v5.4.3
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: unittests
          file: cover.out
