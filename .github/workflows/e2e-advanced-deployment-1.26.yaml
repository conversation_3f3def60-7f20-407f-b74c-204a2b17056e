name: E2E-Advanced-Deployment-1.26

on:
  push:
    branches:
      - master
      - release-*
  pull_request: {}
  workflow_dispatch: {}

# Declare default permissions as read only.
permissions: read-all

env:
  # Common versions
  GO_VERSION: '1.20'
  KIND_VERSION: 'v0.18.0'
  KIND_IMAGE: 'kindest/node:v1.26.3'
  KIND_CLUSTER_NAME: 'ci-testing'

jobs:

  rollout:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          submodules: true
      - name: Setup Go
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: ${{ env.GO_VERSION }}
      - name: Setup Kind Cluster
        uses: helm/kind-action@a1b0e391336a6ee6713a0583f8c6240d70863de3 # v1.12.0
        with:
          node_image: ${{ env.KIND_IMAGE }}
          cluster_name: ${{ env.KIND_CLUSTER_NAME }}
          config: ./test/kind-conf.yaml
          version: ${{ env.KIND_VERSION }}
      - name: Build image
        run: |
          export IMAGE="openkruise/kruise-rollout:e2e-${GITHUB_RUN_ID}"
          docker build --pull --no-cache . -t $IMAGE
          kind load docker-image --name=${KIND_CLUSTER_NAME} $IMAGE || { echo >&2 "kind not installed or error loading image: $IMAGE"; exit 1; }
      - name: Install Kruise
        run: |
          set -ex
          kubectl cluster-info
          make helm
          helm repo add openkruise https://openkruise.github.io/charts/
          helm repo update
          helm install kruise openkruise/kruise --version 1.7.0
          for ((i=1;i<10;i++));
          do
            set +e
            PODS=$(kubectl get pod -n kruise-system | grep '1/1' | grep kruise-controller-manager | wc -l)
            set -e
            if [ "$PODS" -eq "2" ]; then
              break
            fi
            sleep 3
          done
          set +e
          PODS=$(kubectl get pod -n kruise-system | grep '1/1' | grep kruise-controller-manager | wc -l)
          set -e
          if [ "$PODS" -eq "2" ]; then
            echo "Wait for kruise-manager ready successfully"
          else
            echo "Timeout to wait for kruise-manager ready"
            exit 1
          fi
      - name: Install Kruise Rollout
        run: |
          set -ex
          kubectl cluster-info
          IMG=openkruise/kruise-rollout:e2e-${GITHUB_RUN_ID} ./scripts/deploy_kind.sh
          for ((i=1;i<10;i++));
          do
            set +e
            PODS=$(kubectl get pod -n kruise-rollout | grep '1/1' | wc -l)
            set -e
            if [ "$PODS" -eq "1" ]; then
              break
            fi
            sleep 3
          done
          set +e
          PODS=$(kubectl get pod -n kruise-rollout | grep '1/1' | wc -l)
          kubectl get node -o yaml
          kubectl get all -n kruise-rollout -o yaml
          set -e
          if [ "$PODS" -eq "1" ]; then
            echo "Wait for kruise-rollout ready successfully"
          else
            echo "Timeout to wait for kruise-rollout ready"
            exit 1
          fi
      - name: Run E2E Tests For Deployment Controller
        run: |
          export KUBECONFIG=/home/<USER>/.kube/config
          make ginkgo
          set +e
          ./bin/ginkgo -timeout 60m -v --focus='Advanced Deployment' test/e2e
          retVal=$?
          if [ "${retVal}" -ne 0 ];then
              echo "test fail, dump kruise-rollout logs"
              kubectl get pod -n kruise-rollout --no-headers | grep manager | awk '{print $1}' | xargs kubectl logs -n kruise-rollout
          fi
          exit $retVal
      - name: Run E2E Tests For Canary rollout of Advance Deployment
        run: |
          export KUBECONFIG=/home/<USER>/.kube/config
          make ginkgo
          set +e
          ./bin/ginkgo -timeout 60m -v --focus='Canary rollout with Ingress using advance deployment' test/e2e
          retVal=$?
          if [ "${retVal}" -ne 0 ];then
              echo "test fail, dump kruise-rollout logs"
              kubectl get pod -n kruise-rollout --no-headers | grep manager | awk '{print $1}' | xargs kubectl logs -n kruise-rollout
          fi
          exit $retVal
