---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: rollouts.rollouts.kruise.io
spec:
  group: rollouts.kruise.io
  names:
    kind: Rollout
    listKind: RolloutList
    plural: rollouts
    singular: rollout
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The rollout status phase
      jsonPath: .status.phase
      name: STATUS
      type: string
    - description: The rollout canary status step
      jsonPath: .status.canaryStatus.currentStepIndex
      name: CANARY_STEP
      type: integer
    - description: The rollout canary status step state
      jsonPath: .status.canaryStatus.currentStepState
      name: CANARY_STATE
      type: string
    - description: The rollout canary status message
      jsonPath: .status.message
      name: MESSAGE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: Rollout is the Schema for the rollouts API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RolloutSpec defines the desired state of Rollout
            properties:
              disabled:
                default: false
                description: if a rollout disabled, then the rollout would not watch
                  changes of workload
                type: boolean
              objectRef:
                description: |-
                  INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                  ObjectRef indicates workload
                properties:
                  workloadRef:
                    description: |-
                      WorkloadRef contains enough information to let you identify a workload for Rollout
                      Batch release of the bypass
                    properties:
                      apiVersion:
                        description: API Version of the referent
                        type: string
                      kind:
                        description: Kind of the referent
                        type: string
                      name:
                        description: Name of the referent
                        type: string
                    required:
                    - apiVersion
                    - kind
                    - name
                    type: object
                type: object
              rolloutID:
                description: |-
                  DeprecatedRolloutID is the deprecated field.
                  It is recommended that configure RolloutId in workload.annotations[rollouts.kruise.io/rollout-id].
                  RolloutID should be changed before each workload revision publication.
                  It is to distinguish consecutive multiple workload publications and rollout progress.
                type: string
              strategy:
                description: rollout strategy
                properties:
                  canary:
                    description: CanaryStrategy defines parameters for a Replica Based
                      Canary
                    properties:
                      disableGenerateCanaryService:
                        description: canary service will not be generated if DisableGenerateCanaryService
                          is true
                        type: boolean
                      failureThreshold:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          FailureThreshold indicates how many failed pods can be tolerated in all upgraded pods.
                          Only when FailureThreshold are satisfied, Rollout can enter ready state.
                          If FailureThreshold is nil, Rollout will use the MaxUnavailable of workload as its
                          FailureThreshold.
                          Defaults to nil.
                        x-kubernetes-int-or-string: true
                      patchPodTemplateMetadata:
                        description: |-
                          PatchPodTemplateMetadata indicates patch configuration(e.g. labels, annotations) to the canary deployment podTemplateSpec.metadata
                          only support for canary deployment
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            description: annotations
                            type: object
                          labels:
                            additionalProperties:
                              type: string
                            description: labels
                            type: object
                        type: object
                      steps:
                        description: Steps define the order of phases to execute release
                          in batches(20%, 40%, 60%, 80%, 100%)
                        items:
                          description: CanaryStep defines a step of a canary workload.
                          properties:
                            matches:
                              description: |-
                                Matches define conditions used for matching the incoming HTTP requests to canary service.
                                Each match is independent, i.e. this rule will be matched if **any** one of the matches is satisfied.
                                If Gateway API, current only support one match.
                                And cannot support both weight and matches, if both are configured, then matches takes precedence.
                              items:
                                properties:
                                  headers:
                                    description: |-
                                      Headers specifies HTTP request header matchers. Multiple match values are
                                      ANDed together, meaning, a request must match all the specified headers
                                      to select the route.
                                    items:
                                      description: |-
                                        HTTPHeaderMatch describes how to select a HTTP route by matching HTTP request
                                        headers.
                                      properties:
                                        name:
                                          description: |-
                                            Name is the name of the HTTP Header to be matched. Name matching MUST be
                                            case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                            If multiple entries specify equivalent header names, only the first
                                            entry with an equivalent name MUST be considered for a match. Subsequent
                                            entries with an equivalent header name MUST be ignored. Due to the
                                            case-insensitivity of header names, "foo" and "Foo" are considered
                                            equivalent.


                                            When a header is repeated in an HTTP request, it is
                                            implementation-specific behavior as to how this is represented.
                                            Generally, proxies should follow the guidance from the RFC:
                                            https://www.rfc-editor.org/rfc/rfc7230.html#section-3.2.2 regarding
                                            processing a repeated header, with special handling for "Set-Cookie".
                                          maxLength: 256
                                          minLength: 1
                                          pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                          type: string
                                        type:
                                          default: Exact
                                          description: |-
                                            Type specifies how to match against the value of the header.


                                            Support: Core (Exact)


                                            Support: Implementation-specific (RegularExpression)


                                            Since RegularExpression HeaderMatchType has implementation-specific
                                            conformance, implementations can support POSIX, PCRE or any other dialects
                                            of regular expressions. Please read the implementation's documentation to
                                            determine the supported dialect.
                                          enum:
                                          - Exact
                                          - RegularExpression
                                          type: string
                                        value:
                                          description: Value is the value of HTTP
                                            Header to be matched.
                                          maxLength: 4096
                                          minLength: 1
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    maxItems: 16
                                    type: array
                                type: object
                              type: array
                            pause:
                              description: Pause defines a pause stage for a rollout,
                                manual or auto
                              properties:
                                duration:
                                  description: Duration the amount of time to wait
                                    before moving to the next step.
                                  format: int32
                                  type: integer
                              type: object
                            replicas:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Replicas is the number of expected canary pods in this batch
                                it can be an absolute number (ex: 5) or a percentage of total pods.
                              x-kubernetes-int-or-string: true
                            requestHeaderModifier:
                              description: |-
                                Set overwrites the request with the given header (name, value)
                                before the action.


                                Input:
                                  GET /foo HTTP/1.1
                                  my-header: foo


                                requestHeaderModifier:
                                  set:
                                  - name: "my-header"
                                    value: "bar"


                                Output:
                                  GET /foo HTTP/1.1
                                  my-header: bar
                              properties:
                                add:
                                  description: |-
                                    Add adds the given header(s) (name, value) to the request
                                    before the action. It appends to any existing values associated
                                    with the header name.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      add:
                                      - name: "my-header"
                                        value: "bar,baz"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: foo,bar,baz
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                                remove:
                                  description: |-
                                    Remove the given header(s) from the HTTP request before the action. The
                                    value of Remove is a list of HTTP header names. Note that the header
                                    names are case-insensitive (see
                                    https://datatracker.ietf.org/doc/html/rfc2616#section-4.2).


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header1: foo
                                      my-header2: bar
                                      my-header3: baz


                                    Config:
                                      remove: ["my-header1", "my-header3"]


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header2: bar
                                  items:
                                    type: string
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-type: set
                                set:
                                  description: |-
                                    Set overwrites the request with the given header (name, value)
                                    before the action.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      set:
                                      - name: "my-header"
                                        value: "bar"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: bar
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                              type: object
                            weight:
                              description: Weight indicate how many percentage of
                                traffic the canary pods should receive
                              format: int32
                              type: integer
                          type: object
                        type: array
                      trafficRoutings:
                        description: |-
                          TrafficRoutings hosts all the supported service meshes supported to enable more fine-grained traffic routing
                          and current only support one TrafficRouting
                        items:
                          description: TrafficRoutingRef hosts all the different configuration
                            for supported service meshes to enable more fine-grained
                            traffic routing
                          properties:
                            customNetworkRefs:
                              description: CustomNetworkRefs hold a list of custom
                                providers to route traffic
                              items:
                                properties:
                                  apiVersion:
                                    type: string
                                  kind:
                                    type: string
                                  name:
                                    type: string
                                required:
                                - apiVersion
                                - kind
                                - name
                                type: object
                              type: array
                            gateway:
                              description: |-
                                Gateway holds Gateway specific configuration to route traffic
                                Gateway configuration only supports >= v0.4.0 (v1alpha2).
                              properties:
                                httpRouteName:
                                  description: HTTPRouteName refers to the name of
                                    an `HTTPRoute` resource in the same namespace
                                    as the `Rollout`
                                  type: string
                              type: object
                            gracePeriodSeconds:
                              default: 3
                              description: Optional duration in seconds the traffic
                                provider(e.g. nginx ingress controller) consumes the
                                service, ingress configuration changes gracefully.
                              format: int32
                              type: integer
                            ingress:
                              description: Ingress holds Ingress specific configuration
                                to route traffic, e.g. Nginx, Alb.
                              properties:
                                classType:
                                  description: |-
                                    ClassType refers to the type of `Ingress`.
                                    current support nginx, aliyun-alb. default is nginx.
                                  type: string
                                name:
                                  description: Name refers to the name of an `Ingress`
                                    resource in the same namespace as the `Rollout`
                                  type: string
                              required:
                              - name
                              type: object
                            service:
                              description: Service holds the name of a service which
                                selects pods with stable version and don't select
                                any pods with canary version.
                              type: string
                          required:
                          - service
                          type: object
                        type: array
                    type: object
                  paused:
                    description: |-
                      Paused indicates that the Rollout is paused.
                      Default value is false
                    type: boolean
                type: object
            required:
            - objectRef
            - strategy
            type: object
          status:
            description: RolloutStatus defines the observed state of Rollout
            properties:
              canaryStatus:
                description: Canary describes the state of the canary rollout
                properties:
                  canaryReadyReplicas:
                    description: CanaryReadyReplicas the numbers of ready canary revision
                      pods
                    format: int32
                    type: integer
                  canaryReplicas:
                    description: CanaryReplicas the numbers of canary revision pods
                    format: int32
                    type: integer
                  canaryRevision:
                    description: |-
                      CanaryRevision is calculated by rollout based on podTemplateHash, and the internal logic flow uses
                      It may be different from rs podTemplateHash in different k8s versions, so it cannot be used as service selector label
                    type: string
                  currentStepIndex:
                    format: int32
                    type: integer
                  currentStepState:
                    type: string
                  finalisingStep:
                    type: string
                  lastUpdateTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  nextStepIndex:
                    description: |-
                      NextStepIndex defines the next step of the rollout is on.
                      In normal case, NextStepIndex is equal to CurrentStepIndex + 1
                      If the current step is the last step, NextStepIndex is equal to -1
                      Before the release, NextStepIndex is also equal to -1
                      0 is not used and won't appear in any case
                      It is allowed to patch NextStepIndex by design,
                      e.g. if CurrentStepIndex is 2, user can patch NextStepIndex to 3 (if exists) to
                      achieve batch jump, or patch NextStepIndex to 1 to implement a re-execution of step 1
                      Patching it with a non-positive value is meaningless, which will be corrected
                      in the next reconciliation
                      achieve batch jump, or patch NextStepIndex to 1 to implement a re-execution of step 1
                    format: int32
                    type: integer
                  observedRolloutID:
                    description: ObservedRolloutID will record the newest spec.RolloutID
                      if status.canaryRevision equals to workload.updateRevision
                    type: string
                  observedWorkloadGeneration:
                    description: observedWorkloadGeneration is the most recent generation
                      observed for this Rollout ref workload generation.
                    format: int64
                    type: integer
                  podTemplateHash:
                    description: pod template hash is used as service selector label
                    type: string
                  rolloutHash:
                    description: RolloutHash from rollout.spec object
                    type: string
                  stableRevision:
                    description: StableRevision indicates the revision of stable pods
                    type: string
                required:
                - canaryReadyReplicas
                - canaryReplicas
                - canaryRevision
                - currentStepState
                - finalisingStep
                - nextStepIndex
                - podTemplateHash
                type: object
              conditions:
                description: Conditions a list of conditions a rollout can have.
                items:
                  description: RolloutCondition describes the state of a rollout at
                    a certain point.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    lastUpdateTime:
                      description: The last time this condition was updated.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    status:
                      description: Phase of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of rollout condition.
                      type: string
                  required:
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              message:
                description: Message provides details on why the rollout is in its
                  current phase
                type: string
              observedGeneration:
                description: observedGeneration is the most recent generation observed
                  for this Rollout.
                format: int64
                type: integer
              phase:
                description: Phase is the rollout phase.
                type: string
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The rollout status phase
      jsonPath: .status.phase
      name: STATUS
      type: string
    - description: The rollout canary status step
      jsonPath: .status.currentStepIndex
      name: CANARY_STEP
      type: integer
    - description: The rollout canary status step state
      jsonPath: .status.currentStepState
      name: CANARY_STATE
      type: string
    - description: The rollout canary status message
      jsonPath: .status.message
      name: MESSAGE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: Rollout is the Schema for the rollouts API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RolloutSpec defines the desired state of Rollout
            properties:
              disabled:
                default: false
                description: if a rollout disabled, then the rollout would not watch
                  changes of workload
                type: boolean
              strategy:
                description: rollout strategy
                properties:
                  blueGreen:
                    description: BlueGreenStrategy defines parameters for Blue Green
                      Release
                    properties:
                      disableGenerateCanaryService:
                        description: canary service will not be generated if DisableGenerateCanaryService
                          is true
                        type: boolean
                      failureThreshold:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          FailureThreshold indicates how many failed pods can be tolerated in all upgraded pods.
                          Only when FailureThreshold are satisfied, Rollout can enter ready state.
                          If FailureThreshold is nil, Rollout will use the MaxUnavailable of workload as its
                          FailureThreshold.
                          Defaults to nil.
                        x-kubernetes-int-or-string: true
                      steps:
                        description: Steps define the order of phases to execute release
                          in batches(20%, 40%, 60%, 80%, 100%)
                        items:
                          description: CanaryStep defines a step of a canary workload.
                          properties:
                            matches:
                              description: |-
                                Matches define conditions used for matching incoming HTTP requests to the canary service.
                                Each match is independent, i.e. this rule will be matched as long as **any** one of the matches is satisfied.


                                It cannot support Traffic (weight-based routing) and Matches simultaneously, if both are configured.
                                In such cases, Matches takes precedence.
                              items:
                                properties:
                                  headers:
                                    description: |-
                                      Headers specifies HTTP request header matchers. Multiple match values are
                                      ANDed together, meaning, a request must match all the specified headers
                                      to select the route.
                                    items:
                                      description: |-
                                        HTTPHeaderMatch describes how to select a HTTP route by matching HTTP request
                                        headers.
                                      properties:
                                        name:
                                          description: |-
                                            Name is the name of the HTTP Header to be matched. Name matching MUST be
                                            case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                            If multiple entries specify equivalent header names, only the first
                                            entry with an equivalent name MUST be considered for a match. Subsequent
                                            entries with an equivalent header name MUST be ignored. Due to the
                                            case-insensitivity of header names, "foo" and "Foo" are considered
                                            equivalent.


                                            When a header is repeated in an HTTP request, it is
                                            implementation-specific behavior as to how this is represented.
                                            Generally, proxies should follow the guidance from the RFC:
                                            https://www.rfc-editor.org/rfc/rfc7230.html#section-3.2.2 regarding
                                            processing a repeated header, with special handling for "Set-Cookie".
                                          maxLength: 256
                                          minLength: 1
                                          pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                          type: string
                                        type:
                                          default: Exact
                                          description: |-
                                            Type specifies how to match against the value of the header.


                                            Support: Core (Exact)


                                            Support: Implementation-specific (RegularExpression)


                                            Since RegularExpression HeaderMatchType has implementation-specific
                                            conformance, implementations can support POSIX, PCRE or any other dialects
                                            of regular expressions. Please read the implementation's documentation to
                                            determine the supported dialect.
                                          enum:
                                          - Exact
                                          - RegularExpression
                                          type: string
                                        value:
                                          description: Value is the value of HTTP
                                            Header to be matched.
                                          maxLength: 4096
                                          minLength: 1
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    maxItems: 16
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - name
                                    x-kubernetes-list-type: map
                                  path:
                                    description: |-
                                      Path specifies a HTTP request path matcher.
                                      Supported list:
                                      - Istio: https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest
                                      - GatewayAPI: If path is defined, the whole HttpRouteMatch will be used as a standalone matcher
                                    properties:
                                      type:
                                        default: PathPrefix
                                        description: |-
                                          Type specifies how to match against the path Value.


                                          Support: Core (Exact, PathPrefix)


                                          Support: Implementation-specific (RegularExpression)
                                        enum:
                                        - Exact
                                        - PathPrefix
                                        - RegularExpression
                                        type: string
                                      value:
                                        default: /
                                        description: Value of the HTTP path to match
                                          against.
                                        maxLength: 1024
                                        type: string
                                    type: object
                                    x-kubernetes-validations:
                                    - message: value must be an absolute path and
                                        start with '/' when type one of ['Exact',
                                        'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? self.value.startsWith(''/'') : true'
                                    - message: must not contain '//' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''//'') : true'
                                    - message: must not contain '/./' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''/./'') : true'
                                    - message: must not contain '/../' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''/../'') : true'
                                    - message: must not contain '%2f' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''%2f'') : true'
                                    - message: must not contain '%2F' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''%2F'') : true'
                                    - message: must not contain '#' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''#'') : true'
                                    - message: must not end with '/..' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.endsWith(''/..'') : true'
                                    - message: must not end with '/.' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.endsWith(''/.'') : true'
                                    - message: type must be one of ['Exact', 'PathPrefix',
                                        'RegularExpression']
                                      rule: self.type in ['Exact','PathPrefix'] ||
                                        self.type == 'RegularExpression'
                                    - message: must only contain valid characters
                                        (matching ^(?:[-A-Za-z0-9/._~!$&'()*+,;=:@]|[%][0-9a-fA-F]{2})+$)
                                        for types ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? self.value.matches(r"""^(?:[-A-Za-z0-9/._~!$&''()*+,;=:@]|[%][0-9a-fA-F]{2})+$""")
                                        : true'
                                  queryParams:
                                    description: |-
                                      QueryParams specifies HTTP query parameter matchers. Multiple match
                                      values are ANDed together, meaning, a request must match all the
                                      specified query parameters to select the route.
                                      Supported list:
                                      - Istio: https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest
                                      - MSE Ingress: https://help.aliyun.com/zh/ack/ack-managed-and-ack-dedicated/user-guide/annotations-supported-by-mse-ingress-gateways-1
                                        Header/Cookie > QueryParams
                                      - Gateway API
                                    items:
                                      description: |-
                                        HTTPQueryParamMatch describes how to select a HTTP route by matching HTTP
                                        query parameters.
                                      properties:
                                        name:
                                          description: |-
                                            Name is the name of the HTTP query param to be matched. This must be an
                                            exact string match. (See
                                            https://tools.ietf.org/html/rfc7230#section-2.7.3).


                                            If multiple entries specify equivalent query param names, only the first
                                            entry with an equivalent name MUST be considered for a match. Subsequent
                                            entries with an equivalent query param name MUST be ignored.


                                            If a query param is repeated in an HTTP request, the behavior is
                                            purposely left undefined, since different data planes have different
                                            capabilities. However, it is *recommended* that implementations should
                                            match against the first value of the param if the data plane supports it,
                                            as this behavior is expected in other load balancing contexts outside of
                                            the Gateway API.


                                            Users SHOULD NOT route traffic based on repeated query params to guard
                                            themselves against potential differences in the implementations.
                                          maxLength: 256
                                          minLength: 1
                                          pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                          type: string
                                        type:
                                          default: Exact
                                          description: |-
                                            Type specifies how to match against the value of the query parameter.


                                            Support: Extended (Exact)


                                            Support: Implementation-specific (RegularExpression)


                                            Since RegularExpression QueryParamMatchType has Implementation-specific
                                            conformance, implementations can support POSIX, PCRE or any other
                                            dialects of regular expressions. Please read the implementation's
                                            documentation to determine the supported dialect.
                                          enum:
                                          - Exact
                                          - RegularExpression
                                          type: string
                                        value:
                                          description: Value is the value of HTTP
                                            query param to be matched.
                                          maxLength: 1024
                                          minLength: 1
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    maxItems: 16
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - name
                                    x-kubernetes-list-type: map
                                type: object
                              maxItems: 16
                              type: array
                            pause:
                              description: Pause defines a pause stage for a rollout,
                                manual or auto
                              properties:
                                duration:
                                  description: Duration the amount of time to wait
                                    before moving to the next step.
                                  format: int32
                                  type: integer
                              type: object
                            replicas:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Replicas is the number of expected canary pods in this batch
                                it can be an absolute number (ex: 5) or a percentage of total pods.
                              x-kubernetes-int-or-string: true
                            requestHeaderModifier:
                              description: |-
                                Set overwrites the request with the given header (name, value)
                                before the action.


                                Input:
                                  GET /foo HTTP/1.1
                                  my-header: foo


                                requestHeaderModifier:
                                  set:
                                  - name: "my-header"
                                    value: "bar"


                                Output:
                                  GET /foo HTTP/1.1
                                  my-header: bar
                              properties:
                                add:
                                  description: |-
                                    Add adds the given header(s) (name, value) to the request
                                    before the action. It appends to any existing values associated
                                    with the header name.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      add:
                                      - name: "my-header"
                                        value: "bar,baz"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: foo,bar,baz
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                                remove:
                                  description: |-
                                    Remove the given header(s) from the HTTP request before the action. The
                                    value of Remove is a list of HTTP header names. Note that the header
                                    names are case-insensitive (see
                                    https://datatracker.ietf.org/doc/html/rfc2616#section-4.2).


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header1: foo
                                      my-header2: bar
                                      my-header3: baz


                                    Config:
                                      remove: ["my-header1", "my-header3"]


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header2: bar
                                  items:
                                    type: string
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-type: set
                                set:
                                  description: |-
                                    Set overwrites the request with the given header (name, value)
                                    before the action.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      set:
                                      - name: "my-header"
                                        value: "bar"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: bar
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                              type: object
                            traffic:
                              description: |-
                                Traffic indicate how many percentage of traffic the canary pods should receive
                                Value is of string type and is a percentage, e.g. 5%.
                              type: string
                          type: object
                        maxItems: 50
                        type: array
                      trafficRoutingRef:
                        description: TrafficRoutingRef is TrafficRouting's Name
                        type: string
                      trafficRoutings:
                        description: |-
                          TrafficRoutings support ingress, gateway api and custom network resource(e.g. istio, apisix) to enable more fine-grained traffic routing
                          and current only support one TrafficRouting
                        items:
                          description: TrafficRoutingRef hosts all the different configuration
                            for supported service meshes to enable more fine-grained
                            traffic routing
                          properties:
                            customNetworkRefs:
                              description: CustomNetworkRefs hold a list of custom
                                providers to route traffic
                              items:
                                description: ObjectRef holds a references to the Kubernetes
                                  object
                                properties:
                                  apiVersion:
                                    description: API Version of the referent
                                    type: string
                                  kind:
                                    description: Kind of the referent
                                    type: string
                                  name:
                                    description: Name of the referent
                                    type: string
                                required:
                                - apiVersion
                                - kind
                                - name
                                type: object
                              type: array
                            gateway:
                              description: |-
                                Gateway holds Gateway specific configuration to route traffic
                                Gateway configuration only supports >= v0.4.0 (v1alpha2).
                              properties:
                                httpRouteName:
                                  description: HTTPRouteName refers to the name of
                                    an `HTTPRoute` resource in the same namespace
                                    as the `Rollout`
                                  type: string
                              type: object
                            gracePeriodSeconds:
                              default: 3
                              description: Optional duration in seconds the traffic
                                provider(e.g. nginx ingress controller) consumes the
                                service, ingress configuration changes gracefully.
                              format: int32
                              type: integer
                            ingress:
                              description: Ingress holds Ingress specific configuration
                                to route traffic, e.g. Nginx, Alb.
                              properties:
                                classType:
                                  description: |-
                                    ClassType refers to the type of `Ingress`.
                                    current support nginx, aliyun-alb. default is nginx.
                                  type: string
                                name:
                                  description: Name refers to the name of an `Ingress`
                                    resource in the same namespace as the `Rollout`
                                  type: string
                              required:
                              - name
                              type: object
                            service:
                              description: Service holds the name of a service which
                                selects pods with stable version and don't select
                                any pods with canary version.
                              type: string
                          required:
                          - service
                          type: object
                        type: array
                    type: object
                  canary:
                    description: CanaryStrategy defines parameters for a Replica Based
                      Canary
                    properties:
                      disableGenerateCanaryService:
                        description: canary service will not be generated if DisableGenerateCanaryService
                          is true
                        type: boolean
                      enableExtraWorkloadForCanary:
                        description: |-
                          If true, then it will create new deployment for canary, such as: workload-demo-canary.
                          When user verifies that the canary version is ready, we will remove the canary deployment and release the deployment workload-demo in full.
                          Current only support k8s native deployment
                        type: boolean
                      failureThreshold:
                        anyOf:
                        - type: integer
                        - type: string
                        description: |-
                          FailureThreshold indicates how many failed pods can be tolerated in all upgraded pods.
                          Only when FailureThreshold are satisfied, Rollout can enter ready state.
                          If FailureThreshold is nil, Rollout will use the MaxUnavailable of workload as its
                          FailureThreshold.
                          Defaults to nil.
                        x-kubernetes-int-or-string: true
                      patchPodTemplateMetadata:
                        description: |-
                          PatchPodTemplateMetadata indicates patch configuration(e.g. labels, annotations) to the canary deployment podTemplateSpec.metadata
                          only support for canary deployment
                        properties:
                          annotations:
                            additionalProperties:
                              type: string
                            description: annotations
                            type: object
                          labels:
                            additionalProperties:
                              type: string
                            description: labels
                            type: object
                        type: object
                      steps:
                        description: Steps define the order of phases to execute release
                          in batches(20%, 40%, 60%, 80%, 100%)
                        items:
                          description: CanaryStep defines a step of a canary workload.
                          properties:
                            matches:
                              description: |-
                                Matches define conditions used for matching incoming HTTP requests to the canary service.
                                Each match is independent, i.e. this rule will be matched as long as **any** one of the matches is satisfied.


                                It cannot support Traffic (weight-based routing) and Matches simultaneously, if both are configured.
                                In such cases, Matches takes precedence.
                              items:
                                properties:
                                  headers:
                                    description: |-
                                      Headers specifies HTTP request header matchers. Multiple match values are
                                      ANDed together, meaning, a request must match all the specified headers
                                      to select the route.
                                    items:
                                      description: |-
                                        HTTPHeaderMatch describes how to select a HTTP route by matching HTTP request
                                        headers.
                                      properties:
                                        name:
                                          description: |-
                                            Name is the name of the HTTP Header to be matched. Name matching MUST be
                                            case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                            If multiple entries specify equivalent header names, only the first
                                            entry with an equivalent name MUST be considered for a match. Subsequent
                                            entries with an equivalent header name MUST be ignored. Due to the
                                            case-insensitivity of header names, "foo" and "Foo" are considered
                                            equivalent.


                                            When a header is repeated in an HTTP request, it is
                                            implementation-specific behavior as to how this is represented.
                                            Generally, proxies should follow the guidance from the RFC:
                                            https://www.rfc-editor.org/rfc/rfc7230.html#section-3.2.2 regarding
                                            processing a repeated header, with special handling for "Set-Cookie".
                                          maxLength: 256
                                          minLength: 1
                                          pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                          type: string
                                        type:
                                          default: Exact
                                          description: |-
                                            Type specifies how to match against the value of the header.


                                            Support: Core (Exact)


                                            Support: Implementation-specific (RegularExpression)


                                            Since RegularExpression HeaderMatchType has implementation-specific
                                            conformance, implementations can support POSIX, PCRE or any other dialects
                                            of regular expressions. Please read the implementation's documentation to
                                            determine the supported dialect.
                                          enum:
                                          - Exact
                                          - RegularExpression
                                          type: string
                                        value:
                                          description: Value is the value of HTTP
                                            Header to be matched.
                                          maxLength: 4096
                                          minLength: 1
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    maxItems: 16
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - name
                                    x-kubernetes-list-type: map
                                  path:
                                    description: |-
                                      Path specifies a HTTP request path matcher.
                                      Supported list:
                                      - Istio: https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest
                                      - GatewayAPI: If path is defined, the whole HttpRouteMatch will be used as a standalone matcher
                                    properties:
                                      type:
                                        default: PathPrefix
                                        description: |-
                                          Type specifies how to match against the path Value.


                                          Support: Core (Exact, PathPrefix)


                                          Support: Implementation-specific (RegularExpression)
                                        enum:
                                        - Exact
                                        - PathPrefix
                                        - RegularExpression
                                        type: string
                                      value:
                                        default: /
                                        description: Value of the HTTP path to match
                                          against.
                                        maxLength: 1024
                                        type: string
                                    type: object
                                    x-kubernetes-validations:
                                    - message: value must be an absolute path and
                                        start with '/' when type one of ['Exact',
                                        'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? self.value.startsWith(''/'') : true'
                                    - message: must not contain '//' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''//'') : true'
                                    - message: must not contain '/./' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''/./'') : true'
                                    - message: must not contain '/../' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''/../'') : true'
                                    - message: must not contain '%2f' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''%2f'') : true'
                                    - message: must not contain '%2F' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''%2F'') : true'
                                    - message: must not contain '#' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.contains(''#'') : true'
                                    - message: must not end with '/..' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.endsWith(''/..'') : true'
                                    - message: must not end with '/.' when type one
                                        of ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? !self.value.endsWith(''/.'') : true'
                                    - message: type must be one of ['Exact', 'PathPrefix',
                                        'RegularExpression']
                                      rule: self.type in ['Exact','PathPrefix'] ||
                                        self.type == 'RegularExpression'
                                    - message: must only contain valid characters
                                        (matching ^(?:[-A-Za-z0-9/._~!$&'()*+,;=:@]|[%][0-9a-fA-F]{2})+$)
                                        for types ['Exact', 'PathPrefix']
                                      rule: '(self.type in [''Exact'',''PathPrefix''])
                                        ? self.value.matches(r"""^(?:[-A-Za-z0-9/._~!$&''()*+,;=:@]|[%][0-9a-fA-F]{2})+$""")
                                        : true'
                                  queryParams:
                                    description: |-
                                      QueryParams specifies HTTP query parameter matchers. Multiple match
                                      values are ANDed together, meaning, a request must match all the
                                      specified query parameters to select the route.
                                      Supported list:
                                      - Istio: https://istio.io/latest/docs/reference/config/networking/virtual-service/#HTTPMatchRequest
                                      - MSE Ingress: https://help.aliyun.com/zh/ack/ack-managed-and-ack-dedicated/user-guide/annotations-supported-by-mse-ingress-gateways-1
                                        Header/Cookie > QueryParams
                                      - Gateway API
                                    items:
                                      description: |-
                                        HTTPQueryParamMatch describes how to select a HTTP route by matching HTTP
                                        query parameters.
                                      properties:
                                        name:
                                          description: |-
                                            Name is the name of the HTTP query param to be matched. This must be an
                                            exact string match. (See
                                            https://tools.ietf.org/html/rfc7230#section-2.7.3).


                                            If multiple entries specify equivalent query param names, only the first
                                            entry with an equivalent name MUST be considered for a match. Subsequent
                                            entries with an equivalent query param name MUST be ignored.


                                            If a query param is repeated in an HTTP request, the behavior is
                                            purposely left undefined, since different data planes have different
                                            capabilities. However, it is *recommended* that implementations should
                                            match against the first value of the param if the data plane supports it,
                                            as this behavior is expected in other load balancing contexts outside of
                                            the Gateway API.


                                            Users SHOULD NOT route traffic based on repeated query params to guard
                                            themselves against potential differences in the implementations.
                                          maxLength: 256
                                          minLength: 1
                                          pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                          type: string
                                        type:
                                          default: Exact
                                          description: |-
                                            Type specifies how to match against the value of the query parameter.


                                            Support: Extended (Exact)


                                            Support: Implementation-specific (RegularExpression)


                                            Since RegularExpression QueryParamMatchType has Implementation-specific
                                            conformance, implementations can support POSIX, PCRE or any other
                                            dialects of regular expressions. Please read the implementation's
                                            documentation to determine the supported dialect.
                                          enum:
                                          - Exact
                                          - RegularExpression
                                          type: string
                                        value:
                                          description: Value is the value of HTTP
                                            query param to be matched.
                                          maxLength: 1024
                                          minLength: 1
                                          type: string
                                      required:
                                      - name
                                      - value
                                      type: object
                                    maxItems: 16
                                    type: array
                                    x-kubernetes-list-map-keys:
                                    - name
                                    x-kubernetes-list-type: map
                                type: object
                              maxItems: 16
                              type: array
                            pause:
                              description: Pause defines a pause stage for a rollout,
                                manual or auto
                              properties:
                                duration:
                                  description: Duration the amount of time to wait
                                    before moving to the next step.
                                  format: int32
                                  type: integer
                              type: object
                            replicas:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Replicas is the number of expected canary pods in this batch
                                it can be an absolute number (ex: 5) or a percentage of total pods.
                              x-kubernetes-int-or-string: true
                            requestHeaderModifier:
                              description: |-
                                Set overwrites the request with the given header (name, value)
                                before the action.


                                Input:
                                  GET /foo HTTP/1.1
                                  my-header: foo


                                requestHeaderModifier:
                                  set:
                                  - name: "my-header"
                                    value: "bar"


                                Output:
                                  GET /foo HTTP/1.1
                                  my-header: bar
                              properties:
                                add:
                                  description: |-
                                    Add adds the given header(s) (name, value) to the request
                                    before the action. It appends to any existing values associated
                                    with the header name.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      add:
                                      - name: "my-header"
                                        value: "bar,baz"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: foo,bar,baz
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                                remove:
                                  description: |-
                                    Remove the given header(s) from the HTTP request before the action. The
                                    value of Remove is a list of HTTP header names. Note that the header
                                    names are case-insensitive (see
                                    https://datatracker.ietf.org/doc/html/rfc2616#section-4.2).


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header1: foo
                                      my-header2: bar
                                      my-header3: baz


                                    Config:
                                      remove: ["my-header1", "my-header3"]


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header2: bar
                                  items:
                                    type: string
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-type: set
                                set:
                                  description: |-
                                    Set overwrites the request with the given header (name, value)
                                    before the action.


                                    Input:
                                      GET /foo HTTP/1.1
                                      my-header: foo


                                    Config:
                                      set:
                                      - name: "my-header"
                                        value: "bar"


                                    Output:
                                      GET /foo HTTP/1.1
                                      my-header: bar
                                  items:
                                    description: HTTPHeader represents an HTTP Header
                                      name and value as defined by RFC 7230.
                                    properties:
                                      name:
                                        description: |-
                                          Name is the name of the HTTP Header to be matched. Name matching MUST be
                                          case insensitive. (See https://tools.ietf.org/html/rfc7230#section-3.2).


                                          If multiple entries specify equivalent header names, the first entry with
                                          an equivalent name MUST be considered for a match. Subsequent entries
                                          with an equivalent header name MUST be ignored. Due to the
                                          case-insensitivity of header names, "foo" and "Foo" are considered
                                          equivalent.
                                        maxLength: 256
                                        minLength: 1
                                        pattern: ^[A-Za-z0-9!#$%&'*+\-.^_\x60|~]+$
                                        type: string
                                      value:
                                        description: Value is the value of HTTP Header
                                          to be matched.
                                        maxLength: 4096
                                        minLength: 1
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  maxItems: 16
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - name
                                  x-kubernetes-list-type: map
                              type: object
                            traffic:
                              description: |-
                                Traffic indicate how many percentage of traffic the canary pods should receive
                                Value is of string type and is a percentage, e.g. 5%.
                              type: string
                          type: object
                        maxItems: 50
                        type: array
                      trafficRoutingRef:
                        description: TrafficRoutingRef is TrafficRouting's Name
                        type: string
                      trafficRoutings:
                        description: |-
                          TrafficRoutings support ingress, gateway api and custom network resource(e.g. istio, apisix) to enable more fine-grained traffic routing
                          and current only support one TrafficRouting
                        items:
                          description: TrafficRoutingRef hosts all the different configuration
                            for supported service meshes to enable more fine-grained
                            traffic routing
                          properties:
                            customNetworkRefs:
                              description: CustomNetworkRefs hold a list of custom
                                providers to route traffic
                              items:
                                description: ObjectRef holds a references to the Kubernetes
                                  object
                                properties:
                                  apiVersion:
                                    description: API Version of the referent
                                    type: string
                                  kind:
                                    description: Kind of the referent
                                    type: string
                                  name:
                                    description: Name of the referent
                                    type: string
                                required:
                                - apiVersion
                                - kind
                                - name
                                type: object
                              type: array
                            gateway:
                              description: |-
                                Gateway holds Gateway specific configuration to route traffic
                                Gateway configuration only supports >= v0.4.0 (v1alpha2).
                              properties:
                                httpRouteName:
                                  description: HTTPRouteName refers to the name of
                                    an `HTTPRoute` resource in the same namespace
                                    as the `Rollout`
                                  type: string
                              type: object
                            gracePeriodSeconds:
                              default: 3
                              description: Optional duration in seconds the traffic
                                provider(e.g. nginx ingress controller) consumes the
                                service, ingress configuration changes gracefully.
                              format: int32
                              type: integer
                            ingress:
                              description: Ingress holds Ingress specific configuration
                                to route traffic, e.g. Nginx, Alb.
                              properties:
                                classType:
                                  description: |-
                                    ClassType refers to the type of `Ingress`.
                                    current support nginx, aliyun-alb. default is nginx.
                                  type: string
                                name:
                                  description: Name refers to the name of an `Ingress`
                                    resource in the same namespace as the `Rollout`
                                  type: string
                              required:
                              - name
                              type: object
                            service:
                              description: Service holds the name of a service which
                                selects pods with stable version and don't select
                                any pods with canary version.
                              type: string
                          required:
                          - service
                          type: object
                        type: array
                    type: object
                  paused:
                    description: |-
                      Paused indicates that the Rollout is paused.
                      Default value is false
                    type: boolean
                type: object
              workloadRef:
                description: |-
                  INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                  WorkloadRef contains enough information to let you identify a workload for Rollout
                  Batch release of the bypass
                properties:
                  apiVersion:
                    description: API Version of the referent
                    type: string
                  kind:
                    description: Kind of the referent
                    type: string
                  name:
                    description: Name of the referent
                    type: string
                required:
                - apiVersion
                - kind
                - name
                type: object
            required:
            - strategy
            - workloadRef
            type: object
          status:
            description: RolloutStatus defines the observed state of Rollout
            properties:
              blueGreenStatus:
                description: BlueGreen describes the state of the blueGreen rollout
                properties:
                  currentStepIndex:
                    description: CurrentStepIndex defines the current step of the
                      rollout is on.
                    format: int32
                    type: integer
                  currentStepState:
                    type: string
                  finalisingStep:
                    description: FinalisingStep the step of finalising
                    type: string
                  lastUpdateTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  nextStepIndex:
                    description: |-
                      NextStepIndex defines the next step of the rollout is on.
                      In normal case, NextStepIndex is equal to CurrentStepIndex + 1
                      If the current step is the last step, NextStepIndex is equal to -1
                      Before the release, NextStepIndex is also equal to -1
                      0 is not used and won't appear in any case
                      It is allowed to patch NextStepIndex by design,
                      e.g. if CurrentStepIndex is 2, user can patch NextStepIndex to 3 (if exists) to
                      achieve batch jump, or patch NextStepIndex to 1 to implement a re-execution of step 1
                      Patching it with a non-positive value is useless and meaningless, which will be corrected
                      in the next reconciliation
                    format: int32
                    type: integer
                  observedRolloutID:
                    description: ObservedRolloutID will record the newest spec.RolloutID
                      if status.canaryRevision equals to workload.updateRevision
                    type: string
                  observedWorkloadGeneration:
                    description: observedWorkloadGeneration is the most recent generation
                      observed for this Rollout ref workload generation.
                    format: int64
                    type: integer
                  podTemplateHash:
                    description: pod template hash is used as service selector label
                    type: string
                  rolloutHash:
                    description: RolloutHash from rollout.spec object
                    type: string
                  stableRevision:
                    description: StableRevision indicates the revision of stable pods
                    type: string
                  updatedReadyReplicas:
                    description: UpdatedReadyReplicas the numbers of updated ready
                      pods
                    format: int32
                    type: integer
                  updatedReplicas:
                    description: UpdatedReplicas the numbers of updated pods
                    format: int32
                    type: integer
                  updatedRevision:
                    description: |-
                      UpdatedRevision is calculated by rollout based on podTemplateHash, and the internal logic flow uses
                      It may be different from rs podTemplateHash in different k8s versions, so it cannot be used as service selector label
                    type: string
                required:
                - currentStepState
                - finalisingStep
                - nextStepIndex
                - podTemplateHash
                - updatedReadyReplicas
                - updatedReplicas
                - updatedRevision
                type: object
              canaryStatus:
                description: Canary describes the state of the canary rollout
                properties:
                  canaryReadyReplicas:
                    description: CanaryReadyReplicas the numbers of ready canary revision
                      pods
                    format: int32
                    type: integer
                  canaryReplicas:
                    description: CanaryReplicas the numbers of canary revision pods
                    format: int32
                    type: integer
                  canaryRevision:
                    description: |-
                      CanaryRevision is calculated by rollout based on podTemplateHash, and the internal logic flow uses
                      It may be different from rs podTemplateHash in different k8s versions, so it cannot be used as service selector label
                    type: string
                  currentStepIndex:
                    description: CurrentStepIndex defines the current step of the
                      rollout is on.
                    format: int32
                    type: integer
                  currentStepState:
                    type: string
                  finalisingStep:
                    description: FinalisingStep the step of finalising
                    type: string
                  lastUpdateTime:
                    format: date-time
                    type: string
                  message:
                    type: string
                  nextStepIndex:
                    description: |-
                      NextStepIndex defines the next step of the rollout is on.
                      In normal case, NextStepIndex is equal to CurrentStepIndex + 1
                      If the current step is the last step, NextStepIndex is equal to -1
                      Before the release, NextStepIndex is also equal to -1
                      0 is not used and won't appear in any case
                      It is allowed to patch NextStepIndex by design,
                      e.g. if CurrentStepIndex is 2, user can patch NextStepIndex to 3 (if exists) to
                      achieve batch jump, or patch NextStepIndex to 1 to implement a re-execution of step 1
                      Patching it with a non-positive value is useless and meaningless, which will be corrected
                      in the next reconciliation
                    format: int32
                    type: integer
                  observedRolloutID:
                    description: ObservedRolloutID will record the newest spec.RolloutID
                      if status.canaryRevision equals to workload.updateRevision
                    type: string
                  observedWorkloadGeneration:
                    description: observedWorkloadGeneration is the most recent generation
                      observed for this Rollout ref workload generation.
                    format: int64
                    type: integer
                  podTemplateHash:
                    description: pod template hash is used as service selector label
                    type: string
                  rolloutHash:
                    description: RolloutHash from rollout.spec object
                    type: string
                  stableRevision:
                    description: StableRevision indicates the revision of stable pods
                    type: string
                required:
                - canaryReadyReplicas
                - canaryReplicas
                - canaryRevision
                - currentStepState
                - finalisingStep
                - nextStepIndex
                - podTemplateHash
                type: object
              conditions:
                description: Conditions a list of conditions a rollout can have.
                items:
                  description: RolloutCondition describes the state of a rollout at
                    a certain point.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    lastUpdateTime:
                      description: The last time this condition was updated.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    status:
                      description: Phase of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of rollout condition.
                      type: string
                  required:
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              currentStepIndex:
                description: |-
                  These two values will be synchronized with the same fileds in CanaryStatus or BlueGreeenStatus
                  mainly used to provide info for kubectl get command
                format: int32
                type: integer
              currentStepState:
                type: string
              message:
                description: Message provides details on why the rollout is in its
                  current phase
                type: string
              observedGeneration:
                description: observedGeneration is the most recent generation observed
                  for this Rollout.
                format: int64
                type: integer
              phase:
                description: |-
                  BlueGreenStatus *BlueGreenStatus `json:"blueGreenStatus,omitempty"`
                  Phase is the rollout phase.
                type: string
            required:
            - currentStepIndex
            - currentStepState
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
