---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: rollouthistories.rollouts.kruise.io
spec:
  group: rollouts.kruise.io
  names:
    kind: RolloutHistory
    listKind: RolloutHistoryList
    plural: rollouthistories
    singular: rollouthistory
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: RolloutHistory is the Schema for the rollouthistories API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: RolloutHistorySpec defines the desired state of RolloutHistory
            properties:
              rollout:
                description: Rollout indicates information of the rollout related
                  with rollouthistory
                properties:
                  data:
                    description: Data indecates the spec of object ref
                    x-kubernetes-preserve-unknown-fields: true
                  name:
                    description: Name indicates the name of object ref, such as rollout
                      name, workload name, ingress name, etc.
                    type: string
                  rolloutID:
                    description: |-
                      RolloutID indicates the new rollout
                      if there is no new RolloutID this time, ignore it and not execute RolloutHistory
                    type: string
                required:
                - name
                - rolloutID
                type: object
              service:
                description: Service indicates information of the service related
                  with workload
                properties:
                  data:
                    description: Data indecates the spec of object ref
                    x-kubernetes-preserve-unknown-fields: true
                  name:
                    description: Name indicates the name of object ref, such as rollout
                      name, workload name, ingress name, etc.
                    type: string
                required:
                - name
                type: object
              trafficRouting:
                description: TrafficRouting indicates information of traffic route
                  related with workload
                properties:
                  httpRoute:
                    description: HTTPRouteRef indacates information of Gateway API
                    properties:
                      data:
                        description: Data indecates the spec of object ref
                        x-kubernetes-preserve-unknown-fields: true
                      name:
                        description: Name indicates the name of object ref, such as
                          rollout name, workload name, ingress name, etc.
                        type: string
                    required:
                    - name
                    type: object
                  ingress:
                    description: IngressRef indicates information of ingress
                    properties:
                      data:
                        description: Data indecates the spec of object ref
                        x-kubernetes-preserve-unknown-fields: true
                      name:
                        description: Name indicates the name of object ref, such as
                          rollout name, workload name, ingress name, etc.
                        type: string
                    required:
                    - name
                    type: object
                type: object
              workload:
                description: Workload indicates information of the workload, such
                  as cloneset, deployment, advanced statefulset
                properties:
                  apiVersion:
                    description: |-
                      APIVersion defines the versioned schema of this representation of an object.
                      Servers should convert recognized schemas to the latest internal value, and
                      may reject unrecognized values.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
                    type: string
                  data:
                    description: Data indecates the spec of object ref
                    x-kubernetes-preserve-unknown-fields: true
                  kind:
                    description: |-
                      Kind is a string value representing the REST resource this object represents.
                      Servers may infer this from the endpoint the client submits requests to.
                      Cannot be updated.
                      In CamelCase.
                      More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
                    type: string
                  name:
                    description: Name indicates the name of object ref, such as rollout
                      name, workload name, ingress name, etc.
                    type: string
                required:
                - name
                type: object
            type: object
          status:
            description: RolloutHistoryStatus defines the observed state of RolloutHistory
            properties:
              canarySteps:
                description: CanarySteps indicates the pods released each step
                items:
                  description: CanaryStepInfo indicates the pods for a revision
                  properties:
                    canaryStepIndex:
                      description: CanaryStepIndex indicates step this revision
                      format: int32
                      type: integer
                    pods:
                      description: Pods indicates the pods information
                      items:
                        description: Pod indicates the information of a pod, including
                          name, ip, node_name.
                        properties:
                          ip:
                            description: IP indicates the pod ip
                            type: string
                          name:
                            description: Name indicates the node name
                            type: string
                          nodeName:
                            description: NodeName indicates the node which pod is
                              located at
                            type: string
                        type: object
                      type: array
                  type: object
                type: array
              phase:
                description: Phase indicates phase of RolloutHistory, just "" or "completed"
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
