---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.14.0
  name: batchreleases.rollouts.kruise.io
spec:
  group: rollouts.kruise.io
  names:
    kind: BatchRelease
    listKind: BatchReleaseList
    plural: batchreleases
    singular: batchrelease
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.targetReference.workloadRef.kind
      name: KIND
      type: string
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.canaryStatus.currentBatch
      name: BATCH
      type: integer
    - jsonPath: .status.canaryStatus.batchState
      name: BATCH-STATE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BatchReleaseSpec defines how to describe an update between
              different compRevision
            properties:
              releasePlan:
                description: ReleasePlan is the details on how to rollout the resources
                properties:
                  batchPartition:
                    description: |-
                      All pods in the batches up to the batchPartition (included) will have
                      the target resource specification while the rest still is the stable revision.
                      This is designed for the operators to manually rollout.
                      Default is nil, which means no partition and will release all batches.
                      BatchPartition start from 0.
                    format: int32
                    type: integer
                  batches:
                    description: |-
                      Batches is the details on each batch of the ReleasePlan.
                      Users can specify their batch plan in this field, such as:
                      batches:
                      - canaryReplicas: 1  # batches 0
                      - canaryReplicas: 2  # batches 1
                      - canaryReplicas: 5  # batches 2
                      Not that these canaryReplicas should be a non-decreasing sequence.
                    items:
                      description: ReleaseBatch is used to describe how each batch
                        release should be
                      properties:
                        canaryReplicas:
                          anyOf:
                          - type: integer
                          - type: string
                          description: |-
                            CanaryReplicas is the number of upgraded pods that should have in this batch.
                            it can be an absolute number (ex: 5) or a percentage of workload replicas.
                            batches[i].canaryReplicas should less than or equal to batches[j].canaryReplicas if i < j.
                          x-kubernetes-int-or-string: true
                      required:
                      - canaryReplicas
                      type: object
                    type: array
                  enableExtraWorkloadForCanary:
                    description: |-
                      EnableExtraWorkloadForCanary indicates whether to create extra workload for canary
                      True corresponds to RollingStyle "Canary".
                      False corresponds to RollingStyle "Partiton".
                      Ignored in BlueGreen-style.
                      This field is about to deprecate, use RollingStyle instead.
                      If both of them are set, controller will only consider this
                      filed when RollingStyle is empty
                    type: boolean
                  failureThreshold:
                    anyOf:
                    - type: integer
                    - type: string
                    description: |-
                      FailureThreshold indicates how many failed pods can be tolerated in all upgraded pods.
                      Only when FailureThreshold are satisfied, Rollout can enter ready state.
                      If FailureThreshold is nil, Rollout will use the MaxUnavailable of workload as its
                      FailureThreshold.
                      Defaults to nil.
                    x-kubernetes-int-or-string: true
                  finalizingPolicy:
                    description: |-
                      FinalizingPolicy define the behavior of controller when phase enter Finalizing
                      Defaults to "Immediate"
                    type: string
                  patchPodTemplateMetadata:
                    description: |-
                      PatchPodTemplateMetadata indicates patch configuration(e.g. labels, annotations) to the canary deployment podTemplateSpec.metadata
                      only support for canary deployment
                    properties:
                      annotations:
                        additionalProperties:
                          type: string
                        description: annotations
                        type: object
                      labels:
                        additionalProperties:
                          type: string
                        description: labels
                        type: object
                    type: object
                  rollingStyle:
                    description: RollingStyle can be "Canary", "Partiton" or "BlueGreen"
                    type: string
                  rolloutID:
                    description: RolloutID indicates an id for each rollout progress
                    type: string
                required:
                - enableExtraWorkloadForCanary
                type: object
              targetReference:
                description: TargetRef contains the GVK and name of the workload that
                  we need to upgrade to.
                properties:
                  workloadRef:
                    description: |-
                      WorkloadRef contains enough information to let you identify a workload for Rollout
                      Batch release of the bypass
                    properties:
                      apiVersion:
                        description: API Version of the referent
                        type: string
                      kind:
                        description: Kind of the referent
                        type: string
                      name:
                        description: Name of the referent
                        type: string
                    required:
                    - apiVersion
                    - kind
                    - name
                    type: object
                type: object
            required:
            - releasePlan
            - targetReference
            type: object
          status:
            description: BatchReleaseStatus defines the observed state of a release
              plan
            properties:
              canaryStatus:
                description: CanaryStatus describes the state of the canary rollout.
                properties:
                  batchReadyTime:
                    description: |-
                      BatchReadyTime is the ready timestamp of the current batch or the last batch.
                      This field is updated once a batch ready, and the batches[x].pausedSeconds
                      relies on this field to calculate the real-time duration.
                    format: date-time
                    type: string
                  batchState:
                    description: CurrentBatchState indicates the release state of
                      the current batch.
                    type: string
                  currentBatch:
                    description: The current batch the rollout is working on/blocked,
                      it starts from 0
                    format: int32
                    type: integer
                  noNeedUpdateReplicas:
                    description: the number of pods that no need to rollback in rollback
                      scene.
                    format: int32
                    type: integer
                  updatedReadyReplicas:
                    description: UpdatedReadyReplicas is the number upgraded Pods
                      that have a Ready Condition.
                    format: int32
                    type: integer
                  updatedReplicas:
                    description: UpdatedReplicas is the number of upgraded Pods.
                    format: int32
                    type: integer
                required:
                - currentBatch
                type: object
              collisionCount:
                description: |-
                  Count of hash collisions for creating canary Deployment. The controller uses this
                  field as a collision avoidance mechanism when it needs to create the name for the
                  newest canary Deployment.
                format: int32
                type: integer
              conditions:
                description: Conditions represents the observed process state of each
                  phase during executing the release plan.
                items:
                  description: RolloutCondition describes the state of a rollout at
                    a certain point.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    lastUpdateTime:
                      description: The last time this condition was updated.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    status:
                      description: Phase of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of rollout condition.
                      type: string
                  required:
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                description: |-
                  ObservedGeneration is the most recent generation observed for this BatchRelease.
                  It corresponds to this BatchRelease's generation, which is updated on mutation
                  by the API Server, and only if BatchRelease Spec was changed, its generation will increase 1.
                format: int64
                type: integer
              observedReleasePlanHash:
                description: ObservedReleasePlanHash is a hash code of observed itself
                  spec.releasePlan.
                type: string
              observedRolloutID:
                description: |-
                  ObservedRolloutID is the most recent rollout-id observed for this BatchRelease.
                  If RolloutID was changed, we will restart to roll out from batch 0,
                  to ensure the batch-id and rollout-id labels of Pods are correct.
                type: string
              observedWorkloadReplicas:
                description: |-
                  ObservedWorkloadReplicas is observed replicas of target referenced workload.
                  This field is designed to deal with scaling event during rollout, if this field changed,
                  it means that the workload is scaling during rollout.
                format: int32
                type: integer
              phase:
                description: |-
                  Phase is the release plan phase, which indicates the current state of release
                  plan state machine in BatchRelease controller.
                type: string
              stableRevision:
                description: StableRevision is the pod-template-hash of stable revision
                  pod template.
                type: string
              updateRevision:
                description: UpdateRevision is the pod-template-hash of update revision
                  pod template.
                type: string
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - jsonPath: .spec.targetReference.workloadRef.kind
      name: KIND
      type: string
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.canaryStatus.currentBatch
      name: BATCH
      type: integer
    - jsonPath: .status.canaryStatus.batchState
      name: BATCH-STATE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BatchReleaseSpec defines how to describe an update between
              different compRevision
            properties:
              releasePlan:
                description: ReleasePlan is the details on how to rollout the resources
                properties:
                  batchPartition:
                    description: |-
                      All pods in the batches up to the batchPartition (included) will have
                      the target resource specification while the rest still is the stable revision.
                      This is designed for the operators to manually rollout.
                      Default is nil, which means no partition and will release all batches.
                      BatchPartition start from 0.
                    format: int32
                    type: integer
                  batches:
                    description: |-
                      Batches is the details on each batch of the ReleasePlan.
                      Users can specify their batch plan in this field, such as:
                      batches:
                      - canaryReplicas: 1  # batches 0
                      - canaryReplicas: 2  # batches 1
                      - canaryReplicas: 5  # batches 2
                      Not that these canaryReplicas should be a non-decreasing sequence.
                    items:
                      description: ReleaseBatch is used to describe how each batch
                        release should be
                      properties:
                        canaryReplicas:
                          anyOf:
                          - type: integer
                          - type: string
                          description: |-
                            CanaryReplicas is the number of upgraded pods that should have in this batch.
                            it can be an absolute number (ex: 5) or a percentage of workload replicas.
                            batches[i].canaryReplicas should less than or equal to batches[j].canaryReplicas if i < j.
                          x-kubernetes-int-or-string: true
                      required:
                      - canaryReplicas
                      type: object
                    type: array
                  enableExtraWorkloadForCanary:
                    description: |-
                      EnableExtraWorkloadForCanary indicates whether to create extra workload for canary
                      True corresponds to RollingStyle "Canary".
                      False corresponds to RollingStyle "Partiton".
                      Ignored in BlueGreen-style.
                      This field is about to deprecate, use RollingStyle instead.
                      If both of them are set, controller will only consider this
                      filed when RollingStyle is empty
                    type: boolean
                  failureThreshold:
                    anyOf:
                    - type: integer
                    - type: string
                    description: |-
                      FailureThreshold indicates how many failed pods can be tolerated in all upgraded pods.
                      Only when FailureThreshold are satisfied, Rollout can enter ready state.
                      If FailureThreshold is nil, Rollout will use the MaxUnavailable of workload as its
                      FailureThreshold.
                      Defaults to nil.
                    x-kubernetes-int-or-string: true
                  finalizingPolicy:
                    description: |-
                      FinalizingPolicy define the behavior of controller when phase enter Finalizing
                      Defaults to "Immediate"
                    type: string
                  patchPodTemplateMetadata:
                    description: |-
                      PatchPodTemplateMetadata indicates patch configuration(e.g. labels, annotations) to the canary deployment podTemplateSpec.metadata
                      only support for canary deployment
                    properties:
                      annotations:
                        additionalProperties:
                          type: string
                        description: annotations
                        type: object
                      labels:
                        additionalProperties:
                          type: string
                        description: labels
                        type: object
                    type: object
                  rollingStyle:
                    description: RollingStyle can be "Canary", "Partiton" or "BlueGreen"
                    type: string
                  rolloutID:
                    description: RolloutID indicates an id for each rollout progress
                    type: string
                required:
                - enableExtraWorkloadForCanary
                type: object
              workloadRef:
                description: |-
                  WorkloadRef contains enough information to let you identify a workload for Rollout
                  Batch release of the bypass
                properties:
                  apiVersion:
                    description: API Version of the referent
                    type: string
                  kind:
                    description: Kind of the referent
                    type: string
                  name:
                    description: Name of the referent
                    type: string
                required:
                - apiVersion
                - kind
                - name
                type: object
            required:
            - releasePlan
            type: object
          status:
            description: BatchReleaseStatus defines the observed state of a release
              plan
            properties:
              canaryStatus:
                description: CanaryStatus describes the state of the canary rollout.
                properties:
                  batchReadyTime:
                    description: |-
                      BatchReadyTime is the ready timestamp of the current batch or the last batch.
                      This field is updated once a batch ready, and the batches[x].pausedSeconds
                      relies on this field to calculate the real-time duration.
                    format: date-time
                    type: string
                  batchState:
                    description: CurrentBatchState indicates the release state of
                      the current batch.
                    type: string
                  currentBatch:
                    description: The current batch the rollout is working on/blocked,
                      it starts from 0
                    format: int32
                    type: integer
                  noNeedUpdateReplicas:
                    description: the number of pods that no need to rollback in rollback
                      scene.
                    format: int32
                    type: integer
                  updatedReadyReplicas:
                    description: UpdatedReadyReplicas is the number upgraded Pods
                      that have a Ready Condition.
                    format: int32
                    type: integer
                  updatedReplicas:
                    description: UpdatedReplicas is the number of upgraded Pods.
                    format: int32
                    type: integer
                required:
                - currentBatch
                type: object
              collisionCount:
                description: |-
                  Count of hash collisions for creating canary Deployment. The controller uses this
                  field as a collision avoidance mechanism when it needs to create the name for the
                  newest canary Deployment.
                format: int32
                type: integer
              conditions:
                description: Conditions represents the observed process state of each
                  phase during executing the release plan.
                items:
                  description: RolloutCondition describes the state of a rollout at
                    a certain point.
                  properties:
                    lastTransitionTime:
                      description: Last time the condition transitioned from one status
                        to another.
                      format: date-time
                      type: string
                    lastUpdateTime:
                      description: The last time this condition was updated.
                      format: date-time
                      type: string
                    message:
                      description: A human readable message indicating details about
                        the transition.
                      type: string
                    reason:
                      description: The reason for the condition's last transition.
                      type: string
                    status:
                      description: Phase of the condition, one of True, False, Unknown.
                      type: string
                    type:
                      description: Type of rollout condition.
                      type: string
                  required:
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              message:
                description: Message provides details on why the rollout is in its
                  current phase
                type: string
              observedGeneration:
                description: |-
                  ObservedGeneration is the most recent generation observed for this BatchRelease.
                  It corresponds to this BatchRelease's generation, which is updated on mutation
                  by the API Server, and only if BatchRelease Spec was changed, its generation will increase 1.
                format: int64
                type: integer
              observedReleasePlanHash:
                description: ObservedReleasePlanHash is a hash code of observed itself
                  spec.releasePlan.
                type: string
              observedRolloutID:
                description: |-
                  ObservedRolloutID is the most recent rollout-id observed for this BatchRelease.
                  If RolloutID was changed, we will restart to roll out from batch 0,
                  to ensure the batch-id and rollout-id labels of Pods are correct.
                type: string
              observedWorkloadReplicas:
                description: |-
                  ObservedWorkloadReplicas is observed replicas of target referenced workload.
                  This field is designed to deal with scaling event during rollout, if this field changed,
                  it means that the workload is scaling during rollout.
                format: int32
                type: integer
              phase:
                description: |-
                  Phase is the release plan phase, which indicates the current state of release
                  plan state machine in BatchRelease controller.
                type: string
              stableRevision:
                description: StableRevision is the pod-template-hash of stable revision
                  pod template.
                type: string
              updateRevision:
                description: UpdateRevision is the pod-template-hash of update revision
                  pod template.
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
