apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: mutating-webhook-configuration
webhooks:
  - name: munifiedworload.kb.io
    objectSelector:
      matchExpressions:
        - key: rollouts.kruise.io/workload-type
          operator: Exists
  - name: mcloneset.kb.io
    objectSelector:
      matchExpressions:
        - key: rollouts.kruise.io/workload-type
          operator: Exists
  - name: mdaemonset.kb.io
    objectSelector:
      matchExpressions:
        - key: rollouts.kruise.io/workload-type
          operator: Exists
  # - name: mstatefulset.kb.io
  #   objectSelector:
  #     matchExpressions:
  #       - key: rollouts.kruise.io/workload-type
  #         operator: Exists
  # - name: madvancedstatefulset.kb.io
  #   objectSelector:
  #     matchExpressions:
  #       - key: rollouts.kruise.io/workload-type
  #         operator: Exists
  - name: mdeployment.kb.io
    objectSelector:
      matchExpressions:
        - key: control-plane
          operator: NotIn
          values:
            - controller-manager
        - key: rollouts.kruise.io/workload-type
          operator: Exists
