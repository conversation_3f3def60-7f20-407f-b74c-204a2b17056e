---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: manager-role
rules:
- apiGroups:
  - '*'
  resources:
  - events
  verbs:
  - create
  - patch
  - update
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - mutatingwebhookconfigurations
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apiextensions.k8s.io
  resources:
  - customresourcedefinitions
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - apps
  resources:
  - replicasets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - replicasets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - statefulsets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - apps.kruise.io
  resources:
  - clonesets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps.kruise.io
  resources:
  - clonesets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - apps.kruise.io
  resources:
  - daemonsets
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps.kruise.io
  resources:
  - daemonsets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - apps.kruise.io
  resources:
  - statefulsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps.kruise.io
  resources:
  - statefulsets/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - autoscaling
  resources:
  - horizontalpodautoscalers
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - pods
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - pods/finalizers
  verbs:
  - update
- apiGroups:
  - ""
  resources:
  - pods/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - services/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - gateway.networking.k8s.io
  resources:
  - httproutes/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - networking.istio.io
  resources:
  - destinationrules
  - virtualservices
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - networking.k8s.io
  resources:
  - ingresses/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - batchreleases
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rollouts.kruise.io
  resources:
  - batchreleases/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouthistories
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouthistories/finalizers
  verbs:
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouthistories/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouts
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouts/finalizers
  verbs:
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - rollouts/status
  verbs:
  - get
  - patch
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - trafficroutings
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - rollouts.kruise.io
  resources:
  - trafficroutings/finalizers
  verbs:
  - update
- apiGroups:
  - rollouts.kruise.io
  resources:
  - trafficroutings/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: manager-role
  namespace: system
rules:
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
