
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin/
testbin/
vendor/
.temp

# Test binary, build with `go test -c`
*.test
test/e2e/generated/bindata.go

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Kubernetes Generated files - skip generated files, except for vendored files

!vendor/**/zz_generated.*

# editor and IDE paraphernalia
.idea
*.swp
*.swo
*~
.vscode

.DS_Store

lua_configuration/networking.istio.io/**/testdata/*.lua
