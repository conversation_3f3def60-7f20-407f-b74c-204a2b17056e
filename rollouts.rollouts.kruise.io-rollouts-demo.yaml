apiVersion: rollouts.kruise.io/v1alpha1
kind: Rollout
metadata:
  annotations:
    rollouts.kruise.io/hash: d8c8v2w74286d77c4925wdw58v65z8725z7z4cw4f6cc485cvv62vx9cdwfv7b76
    rollouts.kruise.io/rolling-style: canary
  creationTimestamp: "2023-09-25T13:49:00Z"
  deletionGracePeriodSeconds: 0
  deletionTimestamp: "2023-09-25T13:52:34Z"
  generation: 2
  name: rollouts-demo
  namespace: rollout-59e5b9cbeb2ed91f
  resourceVersion: "2699"
  uid: e861a61c-07f6-4064-8486-154bc6d86e29
spec:
  disabled: false
  objectRef:
    workloadRef:
      apiVersion: apps/v1
      kind: Deployment
      name: echoserver
  strategy:
    canary:
      steps:
      - matches:
        - headers:
          - name: user-agent
            type: Exact
            value: pc
        pause: {}
        replicas: 1
      - pause: {}
        weight: 50
      trafficRoutings:
      - customNetworkRefs:
        - apiVersion: networking.istio.io/v1alpha3
          kind: VirtualService
          name: vs-demo
        service: echoserver
status:
  canaryStatus:
    canaryReadyReplicas: 0
    canaryReplicas: 0
    canaryRevision: 684554996d
    currentStepIndex: 2
    currentStepState: Completed
    observedWorkloadGeneration: 1
    podTemplateHash: ""
    rolloutHash: d8c8v2w74286d77c4925wdw58v65z8725z7z4cw4f6cc485cvv62vx9cdwfv7b76
    stableRevision: 567694c97d
  conditions:
  - lastTransitionTime: "2023-09-25T13:50:00Z"
    lastUpdateTime: "2023-09-25T13:50:00Z"
    message: Rollout is in Progressing
    reason: Initializing
    status: "True"
    type: Progressing
  message: workload deployment is completed
  observedGeneration: 1
  phase: Progressing
